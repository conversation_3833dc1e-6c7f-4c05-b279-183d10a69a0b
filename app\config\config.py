import os
from datetime import datetime

class Config:
    # 定义基础路径 - 使用当前目录作为基础路径
    BASE_DIR = os.path.abspath(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
    
    SECRET_KEY = 'your-secret-key-here'  # 使用一个强随机密钥
    
    # 数据库配置 - MySQL (使用fileman用户和新密码)
    SQLALCHEMY_DATABASE_URI = 'mysql+pymysql://fileman:CV24051zhou@localhost/file_manager?charset=utf8mb4'
    
    # 或者使用 PostgreSQL
    # SQLALCHEMY_DATABASE_URI = 'postgresql://fileman:password123@localhost/file_manager'
    
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # 上传文件路径
    UPLOAD_FOLDER = os.path.join(BASE_DIR, 'uploads')
    MAX_CONTENT_LENGTH = 10 * 1024 * 1024 * 1024  # 10GB
    PORT = 2026
    
    # 日志配置
    LOG_DIR = os.path.join(BASE_DIR, 'logs')
    LOG_FILE = os.path.join(LOG_DIR, 'app.log')
    LOG_FORMAT = '%(asctime)s [%(levelname)s] %(message)s'
    LOG_LEVEL = 'INFO'

    # 确保必要的目录存在
    REQUIRED_DIRS = [
        UPLOAD_FOLDER,             # uploads 目录
        LOG_DIR                    # logs 目录
    ]
    
    def __init__(self):
        # 创建必要的目录
        for path in self.REQUIRED_DIRS:
            if path and not os.path.exists(path):
                try:
                    os.makedirs(path)
                except Exception as e:
                    print(f"创建目录失败 {path}: {str(e)}") 