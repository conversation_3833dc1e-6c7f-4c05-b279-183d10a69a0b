-- 删除可能存在的约束和表
SET FOREIGN_KEY_CHECKS = 0;

-- 删除signature表（如果存在）
DROP TABLE IF EXISTS signature;

-- 创建新的signature表
CREATE TABLE signature (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    file_id INT NOT NULL,
    signature_data TEXT NOT NULL,
    signature_date DATETIME,
    signature_metadata TEXT,
    FOREIGN KEY (user_id) REFERENCES user(id),
    FOREIGN KEY (file_id) REFERENCES file(id)
);

-- 删除user_signature表（如果存在）
DROP TABLE IF EXISTS user_signature;

-- 创建新的user_signature表
CREATE TABLE user_signature (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    signature_data TEXT NOT NULL,
    created_at DATETIME,
    is_default BOOLEAN DEFAULT 1,
    description TEXT,
    FOREIGN KEY (user_id) REFERENCES user(id)
);

-- 重新启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 显示成功消息
SELECT 'signature和user_signature表已成功重置' AS message; 