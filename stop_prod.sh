#!/bin/bash

# 停止生产环境服务

# 查找gunicorn进程
PIDS=$(pgrep -f "gunicorn -w 8 -b 0.0.0.0:2026 run:app")

if [ -z "$PIDS" ]; then
    echo "没有找到运行中的服务进程"
    exit 0
fi

# 显示找到的进程
echo "找到以下gunicorn进程:"
ps -f -p $PIDS

# 停止进程
echo "正在停止服务..."
kill $PIDS

# 等待进程结束
sleep 3

# 检查是否还有进程在运行
if pgrep -f "gunicorn -w 4 -b 0.0.0.0:2026 run:app" > /dev/null; then
    echo "进程未能正常结束，尝试强制终止..."
    pkill -9 -f "gunicorn -w 4 -b 0.0.0.0:2026 run:app"
    sleep 1
fi

# 最终确认
if pgrep -f "gunicorn -w 4 -b 0.0.0.0:2026 run:app" > /dev/null; then
    echo "警告: 无法停止所有进程"
    exit 1
else
    echo "服务已成功停止"
    exit 0
fi 