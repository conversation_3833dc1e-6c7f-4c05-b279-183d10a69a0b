<div class="card chunk-uploader-component mb-3">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-cloud-upload-alt"></i> 大文件上传
        </h5>
        <span class="badge bg-info">支持断点续传</span>
    </div>
    <div class="card-body">
        <form id="chunkUploadForm">
            <div class="mb-3">
                <label for="chunkFileInput" class="form-label">选择文件</label>
                <input type="file" class="form-control" id="chunkFileInput">
                <div class="form-text">支持单个大文件，最大 10GB</div>
            </div>
            
            <div class="mb-3">
                <label for="chunkFolderSelect" class="form-label">目标文件夹</label>
                <select class="form-select" id="chunkFolderSelect">
                    <option value="">根目录</option>
                    {% for folder in folders %}
                    <option value="{{ folder.id }}">{{ folder.name }}</option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="upload-progress mb-3 d-none">
                <label class="form-label">上传进度 <span id="progressPercentage">0%</span></label>
                <div class="progress">
                    <div id="chunkProgressBar" class="progress-bar progress-bar-striped progress-bar-animated" style="width: 0%"></div>
                </div>
                <div class="upload-stats mt-2">
                    <small class="text-muted">
                        文件: <span id="uploadFilename">-</span> | 
                        已上传: <span id="uploadedSize">0 KB</span> / <span id="totalSize">0 KB</span> | 
                        速度: <span id="uploadSpeed">0 KB/s</span>
                    </small>
                </div>
            </div>
            
            <div class="d-flex gap-2">
                <button type="button" id="chunkUploadBtn" class="btn btn-primary">
                    <i class="fas fa-upload"></i> 开始上传
                </button>
                <button type="button" id="chunkPauseBtn" class="btn btn-secondary d-none">
                    <i class="fas fa-pause"></i> 暂停
                </button>
                <button type="button" id="chunkResumeBtn" class="btn btn-success d-none">
                    <i class="fas fa-play"></i> 继续
                </button>
                <button type="button" id="chunkCancelBtn" class="btn btn-danger d-none">
                    <i class="fas fa-times"></i> 取消
                </button>
            </div>
        </form>
    </div>
</div>

<script src="{{ url_for('static', filename='js/file-uploader.js') }}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 获取DOM元素
    const fileInput = document.getElementById('chunkFileInput');
    const folderSelect = document.getElementById('chunkFolderSelect');
    const uploadBtn = document.getElementById('chunkUploadBtn');
    const pauseBtn = document.getElementById('chunkPauseBtn');
    const resumeBtn = document.getElementById('chunkResumeBtn');
    const cancelBtn = document.getElementById('chunkCancelBtn');
    const progressBar = document.getElementById('chunkProgressBar');
    const progressPercentage = document.getElementById('progressPercentage');
    const uploadProgress = document.querySelector('.upload-progress');
    const uploadFilename = document.getElementById('uploadFilename');
    const uploadedSize = document.getElementById('uploadedSize');
    const totalSize = document.getElementById('totalSize');
    const uploadSpeed = document.getElementById('uploadSpeed');
    
    // 记录上传时间和大小，用于计算速度
    let lastTime = 0;
    let lastSize = 0;
    let totalBytes = 0;
    
    // 格式化文件大小
    function formatSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    // 计算上传速度
    function calculateSpeed(loadedBytes) {
        const now = Date.now();
        const timeElapsed = (now - lastTime) / 1000; // 转换为秒
        if (timeElapsed > 0) {
            const bytesPerSecond = (loadedBytes - lastSize) / timeElapsed;
            lastTime = now;
            lastSize = loadedBytes;
            return formatSize(bytesPerSecond) + '/s';
        }
        return '计算中...';
    }
    
    // 创建分片上传器实例
    const uploader = new ChunkUploader({
        // 上传进度回调
        onProgress: (progress) => {
            progressBar.style.width = `${progress}%`;
            progressPercentage.textContent = `${progress}%`;
            
            // 计算已上传大小
            const loadedBytes = Math.floor(totalBytes * (progress / 100));
            uploadedSize.textContent = formatSize(loadedBytes);
            
            // 计算上传速度
            uploadSpeed.textContent = calculateSpeed(loadedBytes);
        },
        // 上传成功回调
        onSuccess: (result) => {
            console.log('上传成功:', result);
            // 隐藏暂停和继续按钮
            pauseBtn.classList.add('d-none');
            resumeBtn.classList.add('d-none');
            cancelBtn.classList.add('d-none');
            
            // 重新启用上传按钮
            uploadBtn.disabled = false;
            uploadBtn.textContent = '上传完成!';
            setTimeout(() => {
                uploadBtn.innerHTML = '<i class="fas fa-upload"></i> 开始上传';
            }, 3000);
            
            // 显示成功消息
            progressBar.classList.remove('progress-bar-animated');
            progressBar.classList.add('bg-success');
            
            // 刷新文件列表（如果需要）
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        },
        // 上传错误回调
        onError: (error) => {
            console.error('上传失败:', error);
            alert(`上传失败: ${error.message}`);
            
            // 重新启用上传按钮
            uploadBtn.disabled = false;
            uploadBtn.innerHTML = '<i class="fas fa-upload"></i> 重新上传';
            
            // 隐藏暂停和继续按钮
            pauseBtn.classList.add('d-none');
            resumeBtn.classList.add('d-none');
            
            // 显示取消按钮
            cancelBtn.classList.remove('d-none');
            
            // 显示错误状态
            progressBar.classList.remove('progress-bar-animated');
            progressBar.classList.add('bg-danger');
        },
        // 暂停回调
        onPaused: () => {
            progressBar.classList.remove('progress-bar-animated');
            pauseBtn.classList.add('d-none');
            resumeBtn.classList.remove('d-none');
        },
        // 恢复回调
        onResumed: () => {
            progressBar.classList.add('progress-bar-animated');
            pauseBtn.classList.remove('d-none');
            resumeBtn.classList.add('d-none');
        }
    });
    
    // 上传按钮点击事件
    uploadBtn.addEventListener('click', () => {
        if (fileInput.files.length === 0) {
            alert('请先选择文件');
            return;
        }
        
        const file = fileInput.files[0];
        const folderId = folderSelect.value ? parseInt(folderSelect.value) : null;
        
        // 更新UI
        uploadFilename.textContent = file.name;
        totalSize.textContent = formatSize(file.size);
        uploadedSize.textContent = '0 B';
        uploadSpeed.textContent = '计算中...';
        progressBar.style.width = '0%';
        progressBar.classList.remove('bg-success', 'bg-danger');
        progressBar.classList.add('progress-bar-animated');
        progressPercentage.textContent = '0%';
        
        // 显示进度条和控制按钮
        uploadProgress.classList.remove('d-none');
        pauseBtn.classList.remove('d-none');
        cancelBtn.classList.remove('d-none');
        resumeBtn.classList.add('d-none');
        
        // 禁用上传按钮
        uploadBtn.disabled = true;
        
        // 记录开始时间和总大小
        lastTime = Date.now();
        lastSize = 0;
        totalBytes = file.size;
        
        // 开始上传
        uploader.upload(file, folderId);
    });
    
    // 暂停按钮点击事件
    pauseBtn.addEventListener('click', () => {
        uploader.pause();
    });
    
    // 继续按钮点击事件
    resumeBtn.addEventListener('click', () => {
        uploader.resume();
    });
    
    // 取消按钮点击事件
    cancelBtn.addEventListener('click', () => {
        uploader.cancel();
        
        // 更新UI
        uploadProgress.classList.add('d-none');
        pauseBtn.classList.add('d-none');
        resumeBtn.classList.add('d-none');
        cancelBtn.classList.add('d-none');
        
        // 重置上传按钮
        uploadBtn.disabled = false;
        uploadBtn.innerHTML = '<i class="fas fa-upload"></i> 开始上传';
    });
});
</script> 