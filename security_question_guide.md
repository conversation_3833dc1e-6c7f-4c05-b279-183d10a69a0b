# cv24051用户登录安全问题验证指南

本文档介绍如何为cv24051超级管理员用户设置安全问题并使用安全问题进行登录验证。

## 功能概述

安全问题验证是一种额外的安全层，专为超级管理员账户(cv24051)设计。启用此功能后，超级管理员在登录时需要完成两步验证：
1. 输入正确的用户名和密码
2. 回答预先设置的安全问题

这大大提高了超级管理员账户的安全性，即使密码泄露，未经授权的用户也无法登录系统。

## 设置安全问题

### 步骤1: 运行安全问题设置脚本

```bash
python add_security_question.py
```

### 步骤2: 按照提示操作

1. 脚本会自动检测并创建必要的数据库列
2. 选择一个预设安全问题或创建自定义问题
3. 输入并确认安全问题的答案
4. 完成设置

示例输出:
```
正在设置安全问题功能...
添加安全问题列...
安全问题列已添加
添加安全问题答案列...
安全问题答案列已添加

为cv24051用户设置安全问题
---------------------------

预设安全问题:
1. 大哥的名字是?
2. 您的第一所学校的名称是什么?
3. 您的第一个宠物的名字是什么?
4. 开发者的名字是?
5. 您最喜欢的老师的名字是什么?
6. 自定义问题

请选择问题 (输入数字): 1

请输入安全问题的答案（注意: 答案不区分大小写，但会忽略前后空格）
答案: 
确认答案: 

成功设置安全问题和答案
问题: 您的出生城市是哪里?
提示: 答案已加密存储，请记住您的答案
```

## 使用安全问题登录

1. 输入用户名(cv24051)、密码和验证码
2. 系统会自动重定向到安全问题验证页面
3. 输入安全问题的答案
4. 验证成功后将登录到系统

## 注意事项

1. **安全问题答案**:
   - 不区分大小写
   - 会自动移除前后空格
   - 请记住准确的答案，没有密码找回功能

2. **忘记安全问题答案**:
   如果忘记安全问题答案，请按照以下步骤操作：
   
   ```bash
   # 进入SQLite数据库
   sqlite3 instance/file_manager.db
   
   # 在SQLite提示符下执行:
   UPDATE user SET security_question = NULL, security_answer_hash = NULL WHERE username = 'cv24051';
   .exit
   
   # 然后重新设置安全问题
   python add_security_question.py
   ```

3. **仅适用于cv24051用户**:
   - 安全问题验证仅适用于cv24051超级管理员用户
   - 其他用户登录时不会要求回答安全问题

## 安全建议

1. 选择个人且难以猜测的安全问题和答案
2. 不要与他人分享安全问题答案
3. 定期更换安全问题和答案以提高安全性 