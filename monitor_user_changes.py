# 创建正确的SQLite监控脚本
cat > monitor_user_changes.py << 'EOF'
import sqlite3
import os
import datetime
import json

# 数据库路径
db_path = 'instance/file_manager.db'

# 检查SQLite文件是否存在
if not os.path.exists(db_path):
    print(f"错误: 数据库文件 {db_path} 不存在")
    exit(1)

# 获取当前时间
now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

# 连接到SQLite数据库
conn = sqlite3.connect(db_path)
conn.row_factory = sqlite3.Row
cursor = conn.cursor()

# 获取用户数据
cursor.execute("SELECT id, username, is_admin FROM user")
users = [dict(row) for row in cursor.fetchall()]

# 用户数量
user_count = len(users)

# 获取用户-组关系
try:
    cursor.execute("""
        SELECT u.id, u.username, g.id as group_id, g.name as group_name 
        FROM user u
        JOIN user_group_members ugm ON u.id = ugm.user_id
        JOIN user_group g ON g.id = ugm.group_id
    """)
    user_groups = [dict(row) for row in cursor.fetchall()]
except sqlite3.Error:
    user_groups = []
    print("注意: 无法获取用户-组关系或关系表不存在")

# 获取数据库统计信息
cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
tables = cursor.fetchall()
table_stats = {}
for table in tables:
    table_name = table[0]
    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
    count = cursor.fetchone()[0]
    table_stats[table_name] = count

# 关闭连接
conn.close()

# 输出结果
print(f"\n=== SQLite 用户数据库状态 ({now}) ===")
print(f"用户总数: {user_count}")

print("\n前10名用户:")
for user in users[:10]:
    print(f"ID: {user['id']}, 用户名: {user['username']}, 管理员: {'是' if user['is_admin'] else '否'}")

print("\n表统计:")
for table, count in table_stats.items():
    print(f"{table}: {count} 行")

# 保存到日志文件
log_data = {
    "timestamp": now,
    "user_count": user_count,
    "users": users,
    "user_groups": user_groups,
    "table_stats": table_stats
}

log_dir = "db_logs"
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

log_file = os.path.join(log_dir, f"db_state_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
with open(log_file, 'w', encoding='utf-8') as f:
    json.dump(log_data, f, ensure_ascii=False, indent=2)

print(f"\n日志已保存到: {log_file}")
EOF