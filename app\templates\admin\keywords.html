{% extends "base.html" %}
{% block content %}
<div class="container-fluid p-0">
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h3 class="card-title">按文件夹筛选关键字</h3>
                    <form method="GET" class="d-flex gap-3 align-items-center">
                        <select class="form-select" name="filter_folder" id="filter_folder" onchange="this.form.submit()">
                            <option value="">所有关键字</option>
                            <option value="global" {% if filter_folder == 'global' %}selected{% endif %}>仅全局关键字</option>
                            {% for folder in folders %}
                            <option value="{{ folder.id }}" {% if filter_folder == folder.id|string %}selected{% endif %}>{{ folder.name }}</option>
                            {% endfor %}
                        </select>
                        <span class="ms-3">关键字类型：</span>
                        <select class="form-select" name="keyword_type" id="keyword_type" onchange="this.form.submit()">
                            <option value="">所有类型</option>
                            <option value="1" {% if keyword_type == '1' %}selected{% endif %}>关键字1</option>
                            <option value="2" {% if keyword_type == '2' %}selected{% endif %}>关键字2</option>
                        </select>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-5">
            <div class="card">
                <div class="card-body">
                    <h3 class="card-title">添加新关键字</h3>
                    <form method="POST">
                        <div class="mb-3">
                            <label for="keyword" class="form-label">关键字</label>
                            <input type="text" class="form-control" id="keyword" name="keyword" required>
                            <div class="form-text">关键字将用于筛选文件，例如：合同、报告、报表等</div>
                        </div>
                        <div class="mb-3">
                            <label for="folder_id" class="form-label">指定文件夹(可选)</label>
                            <select class="form-select" id="folder_id" name="folder_id">
                                <option value="">全局关键字(所有文件夹)</option>
                                {% for folder in folders %}
                                <option value="{{ folder.id }}">{{ folder.name }}</option>
                                {% endfor %}
                            </select>
                            <div class="form-text">如果选择文件夹，此关键字将只在该文件夹中可用</div>
                        </div>
                        <div class="mb-3">
                            <label for="keyword_type" class="form-label">关键字类型</label>
                            <select class="form-select" id="keyword_type" name="keyword_type">
                                <option value="0">通用关键字</option>
                                <option value="1">关键字1</option>
                                <option value="2">关键字2</option>
                            </select>
                            <div class="form-text">选择关键字类型，决定在哪个下拉框中显示</div>
                        </div>
                        <button type="submit" class="btn btn-primary">添加关键字</button>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-7">
            <div class="card">
                <div class="card-body">
                    <h3 class="card-title">关键字列表</h3>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>关键字</th>
                                    <th>所属文件夹</th>
                                    <th>类型</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for keyword in keywords %}
                                <tr>
                                    <td>{{ keyword.word }}</td>
                                    <td>
                                        {% if keyword.folder %}
                                        {{ keyword.folder.name }}
                                        {% else %}
                                        <span class="badge bg-secondary">全局</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if keyword.keyword_type == 1 %}
                                        <span class="badge bg-primary">关键字1</span>
                                        {% elif keyword.keyword_type == 2 %}
                                        <span class="badge bg-success">关键字2</span>
                                        {% else %}
                                        <span class="badge bg-info">通用</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ keyword.create_date|format_datetime }}</td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-danger" 
                                                onclick="deleteKeyword({{ keyword.id }}, '{{ keyword.word }}')">
                                            删除
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                                {% if not keywords %}
                                <tr>
                                    <td colspan="5" class="text-center">暂无关键字</td>
                                </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function deleteKeyword(keywordId, word) {
    if (confirm(`确定要删除关键字"${word}"吗？`)) {
        fetch(`/admin/keywords/delete/${keywordId}`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.reload();
            } else {
                alert(data.message || '删除失败');
            }
        })
        .catch(error => alert('操作失败'));
    }
}
</script>
{% endblock %} 