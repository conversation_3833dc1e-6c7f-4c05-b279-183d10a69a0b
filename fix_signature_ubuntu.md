# 修复电子签名功能

## 问题描述

系统在访问签名页面时出现以下错误：

```
[2025-05-29 08:29:55,137] ERROR in routes: 访问签名页面失败: (pymysql.err.OperationalError) (1054, "Unknown column 'signature.file_id' in 'field list'")
[SQL: SELECT signature.id AS signature_id, signature.user_id AS signature_user_id, signature.file_id AS signature_file_id, signature.signature_data AS signature_signature_data, signature.signature_date AS signature_signature_date, signature.signature_metadata AS signature_signature_metadata
FROM signature
WHERE signature.file_id = %(file_id_1)s]
[parameters: {'file_id_1': 859}]
```

这个错误表明数据库的 `signature` 表中缺少 `file_id` 字段。

## 解决方案

根据您的数据库类型，我们提供了两个修复脚本：

1. `fix_signature_table.py` - 用于SQLite数据库（开发环境）
2. `fix_signature_mysql.py` - 用于MySQL数据库（生产环境）

### 在Ubuntu上运行MySQL修复脚本

1. 首先安装必要的依赖：
   ```bash
   pip3 install pymysql
   ```

2. 设置MySQL连接环境变量（根据实际情况修改）：
   ```bash
   export MYSQL_HOST=localhost
   export MYSQL_USER=用户名
   export MYSQL_PASSWORD=密码
   export MYSQL_DB=数据库名
   ```

3. 将 `fix_signature_mysql.py` 脚本复制到服务器上的项目目录中

4. 确保脚本有执行权限：
   ```bash
   chmod +x fix_signature_mysql.py
   ```

5. 执行脚本：
   ```bash
   python3 fix_signature_mysql.py
   ```

6. 检查输出日志，确认修复成功

7. 重启Web服务：
   ```bash
   ./stop_prod.sh
   ./start_prod.sh
   ```

### SQLite修复方法（开发环境）

如果您在开发环境中使用SQLite：

1. 将 `fix_signature_table.py` 脚本复制到项目目录中
2. 执行脚本：
   ```bash
   python3 fix_signature_table.py
   ```

### MySQL脚本说明

MySQL修复脚本会：

1. 连接到MySQL数据库（使用环境变量或默认值）
2. 检查 `signature` 表是否存在，如果不存在，创建新表
3. 如果表存在但缺少 `file_id` 字段，会尝试使用三种方法修复：
   - 方法1：直接添加 `file_id` 列和外键约束
   - 方法2：备份并重建表
   - 方法3：删除旧表并创建新表
4. 检查 `user_signature` 表是否存在，如果不存在，创建该表

### 手动修复方法（MySQL版）

如果自动修复脚本无法运行，也可以通过以下SQL语句手动修复MySQL数据库：

```sql
-- 方法1：尝试直接添加列
ALTER TABLE signature ADD COLUMN file_id INT NOT NULL DEFAULT 0;
ALTER TABLE signature ADD CONSTRAINT fk_signature_file FOREIGN KEY (file_id) REFERENCES file(id);

-- 或者方法2：备份并重建表
CREATE TABLE signature_backup LIKE signature;
INSERT INTO signature_backup SELECT * FROM signature;
DROP TABLE signature;
CREATE TABLE signature (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    file_id INT NOT NULL,
    signature_data TEXT NOT NULL,
    signature_date DATETIME,
    signature_metadata TEXT,
    FOREIGN KEY (user_id) REFERENCES user(id),
    FOREIGN KEY (file_id) REFERENCES file(id)
);
INSERT INTO signature (id, user_id, signature_data, signature_date, signature_metadata, file_id)
SELECT id, user_id, signature_data, signature_date, signature_metadata, 0
FROM signature_backup;
```

## 验证修复

修复后，访问文件签名页面应该不再出现错误，用户应该能够添加和管理电子签名。 