#!/usr/bin/env python3
import os
import sys
import time
import shutil
import logging
import sqlite3
import zipfile
import argparse
from datetime import datetime

# 获取脚本所在目录
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
# 切换到脚本所在目录
os.chdir(SCRIPT_DIR)

# 设置日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join('logs', 'backup.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('backup')

# 确保日志目录存在
if not os.path.exists('logs'):
    os.makedirs('logs')

def get_db_path():
    """获取数据库文件路径"""
    db_path = os.path.join('instance', 'file_manager.db')
    if not os.path.exists(db_path):
        logger.error(f"数据库文件不存在: {db_path}")
        return None
    return db_path

def check_db_integrity(db_path):
    """检查数据库完整性"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("PRAGMA integrity_check")
        result = cursor.fetchone()
        conn.close()
        if result[0] == "ok":
            return True
        logger.error(f"数据库完整性检查失败: {result[0]}")
        return False
    except Exception as e:
        logger.error(f"数据库完整性检查出错: {str(e)}")
        return False

def create_backup_dir(backup_root='backups'):
    """创建备份目录结构"""
    if not os.path.exists(backup_root):
        os.makedirs(backup_root)
    
    # 按日期创建子目录
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_dir = os.path.join(backup_root, timestamp)
    
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)
        logger.info(f"创建备份目录: {backup_dir}")
    
    return backup_dir

def backup_database(backup_dir):
    """备份数据库文件"""
    db_path = get_db_path()
    if not db_path:
        return False
    
    if not check_db_integrity(db_path):
        logger.warning("数据库完整性检查未通过，仍将继续备份")
    
    db_backup_path = os.path.join(backup_dir, 'file_manager.db')
    try:
        # 复制数据库文件
        shutil.copy2(db_path, db_backup_path)
        logger.info(f"数据库备份完成: {db_backup_path}")
        return True
    except Exception as e:
        logger.error(f"数据库备份失败: {str(e)}")
        return False

def backup_config_files(backup_dir):
    """备份配置文件"""
    config_files = [
        os.path.join('app', 'config', 'config.py'),
        os.path.join('instance', 'config.py'),
        'requirements.txt',
        'uwsgi.ini',
        'start_prod.sh',
        'stop_prod.sh'
    ]
    
    config_backup_dir = os.path.join(backup_dir, 'configs')
    if not os.path.exists(config_backup_dir):
        os.makedirs(config_backup_dir)
    
    success_count = 0
    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                # 保持目录结构
                target_dir = os.path.dirname(os.path.join(config_backup_dir, config_file))
                if not os.path.exists(target_dir):
                    os.makedirs(target_dir)
                
                shutil.copy2(config_file, os.path.join(config_backup_dir, config_file))
                logger.info(f"配置文件备份完成: {config_file}")
                success_count += 1
            except Exception as e:
                logger.error(f"配置文件备份失败 {config_file}: {str(e)}")
    
    return success_count > 0

def create_backup_archive(backup_dir, compression=zipfile.ZIP_DEFLATED):
    """将备份目录打包成ZIP文件"""
    try:
        archive_name = f"{os.path.basename(backup_dir)}_backup"
        archive_path = os.path.join(os.path.dirname(backup_dir), f"{archive_name}.zip")
        
        with zipfile.ZipFile(archive_path, 'w', compression) as backup_zip:
            for root, dirs, files in os.walk(backup_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arcname = os.path.relpath(file_path, os.path.dirname(backup_dir))
                    backup_zip.write(file_path, arcname)
        
        logger.info(f"备份压缩包创建成功: {archive_path}")
        return archive_path
    except Exception as e:
        logger.error(f"创建备份压缩包失败: {str(e)}")
        return None

def cleanup_old_backups(backup_root='backups', keep_days=30):
    """清理旧备份文件"""
    if not os.path.exists(backup_root):
        return
    
    now = time.time()
    deleted_count = 0
    
    for item in os.listdir(backup_root):
        item_path = os.path.join(backup_root, item)
        
        # 跳过非备份文件
        if not (os.path.isdir(item_path) or item.endswith('.zip')):
            continue
        
        # 检查文件年龄
        file_age_days = (now - os.path.getmtime(item_path)) / (24 * 3600)
        
        if file_age_days > keep_days:
            try:
                if os.path.isdir(item_path):
                    shutil.rmtree(item_path)
                else:
                    os.remove(item_path)
                deleted_count += 1
                logger.info(f"删除过期备份: {item_path} (已超过{keep_days}天)")
            except Exception as e:
                logger.error(f"删除旧备份文件失败 {item_path}: {str(e)}")
    
    if deleted_count > 0:
        logger.info(f"共清理 {deleted_count} 个过期备份文件")

def run_backup(compress=True, clean=True, keep_days=30):
    """执行完整备份流程"""
    logger.info("开始系统备份...")
    
    # 创建备份目录
    backup_dir = create_backup_dir()
    
    # 备份数据库
    db_backup_success = backup_database(backup_dir)
    
    # 备份配置文件
    config_backup_success = backup_config_files(backup_dir)
    
    # 打包备份
    archive_path = None
    if compress and (db_backup_success or config_backup_success):
        archive_path = create_backup_archive(backup_dir)
        if archive_path:
            # 备份已打包成ZIP，可以删除原始备份目录
            shutil.rmtree(backup_dir)
    
    # 清理旧备份
    if clean:
        cleanup_old_backups(keep_days=keep_days)
    
    if db_backup_success or config_backup_success:
        logger.info("系统备份完成")
        return True
    else:
        logger.error("系统备份失败")
        return False

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='文件管理系统备份工具')
    parser.add_argument('--no-compress', action='store_true', help='不压缩备份文件')
    parser.add_argument('--no-clean', action='store_true', help='不清理旧备份')
    parser.add_argument('--keep-days', type=int, default=30, help='保留备份的天数 (默认: 30)')
    
    args = parser.parse_args()
    
    run_backup(
        compress=not args.no_compress,
        clean=not args.no_clean,
        keep_days=args.keep_days
    ) 