#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
自动修复PDF手写签名功能
此脚本会自动修改HTML模板和JavaScript代码，修复签名功能问题
"""

import os
import re
import sys
import shutil
from datetime import datetime

# 配置
TEMPLATE_DIR = "app/templates/preview"
PDF_SIGN_TEMPLATE = os.path.join(TEMPLATE_DIR, "pdf_sign.html")
BACKUP_DIR = "backups"

def ensure_dir(directory):
    """确保目录存在"""
    if not os.path.exists(directory):
        os.makedirs(directory)
        print(f"创建目录: {directory}")

def backup_file(file_path):
    """备份文件"""
    if not os.path.exists(file_path):
        print(f"错误: 文件不存在 {file_path}")
        return False
        
    ensure_dir(BACKUP_DIR)
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    backup_name = f"{os.path.basename(file_path)}.{timestamp}.bak"
    backup_path = os.path.join(BACKUP_DIR, backup_name)
    
    try:
        shutil.copy2(file_path, backup_path)
        print(f"已备份文件: {file_path} -> {backup_path}")
        return True
    except Exception as e:
        print(f"备份文件时出错: {str(e)}")
        return False

def fix_pdf_sign_template():
    """修复PDF签名模板"""
    if not os.path.exists(PDF_SIGN_TEMPLATE):
        print(f"错误: 未找到PDF签名模板 {PDF_SIGN_TEMPLATE}")
        return False
        
    # 备份文件
    if not backup_file(PDF_SIGN_TEMPLATE):
        return False
    
    try:
        with open(PDF_SIGN_TEMPLATE, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 1. 修复iframe和签名层的z-index
        content = re.sub(
            r'<iframe id="pdfViewer" src="\{\{ file_url \}\}" style="([^"]*)"',
            '<iframe id="pdfViewer" src="{{ file_url }}" style="\\1 z-index: 1;"',
            content
        )
        
        content = re.sub(
            r'<div id="signatureLayer" class="position-absolute top-0 start-0 w-100 h-100" style="([^"]*)"',
            '<div id="signatureLayer" class="position-absolute top-0 start-0 w-100 h-100" style="\\1 z-index: 2;"',
            content
        )
        
        # 2. 修复CSS样式
        style_fixes = """
    .signature-item {
        position: absolute;
        cursor: move;
        z-index: 100;
        pointer-events: auto !important;
    }
    
    .signature-image {
        max-width: 200px;
        border: 1px dashed transparent;
    }
    
    .signature-item:hover .signature-image {
        border-color: #007bff;
    }
    
    .signature-controls {
        display: none;
        z-index: 101;
    }
    
    .signature-item:hover .signature-controls {
        display: block;
    }
    
    /* 确保签名层位于iframe之上 */
    #signatureLayer {
        z-index: 50;
        pointer-events: auto !important;
    }
    
    /* 确保iframe不会拦截鼠标事件 */
    #pdfViewer {
        pointer-events: none;
    }
    
    /* 确保签名模态框显示在最上层 */
    .modal {
        z-index: 9999;
    }
    
    /* 修复移动设备上的触摸事件 */
    @media (hover: none) and (pointer: coarse) {
        #signatureLayer {
            touch-action: none;
        }
        
        .signature-item {
            touch-action: none;
        }
    }"""
        
        # 替换样式部分
        style_pattern = r'<style>(.*?)</style>'
        if re.search(style_pattern, content, re.DOTALL):
            content = re.sub(style_pattern, f'<style>{style_fixes}</style>', content, flags=re.DOTALL)
        else:
            # 如果没有找到样式标签，添加到适当位置
            content = content.replace('</head>', f'<style>{style_fixes}</style>\n</head>')
        
        # 3. 修复JavaScript代码
        # 查找脚本开始位置
        script_start = content.find('<script>')
        script_end = content.find('</script>', script_start)
        
        if script_start != -1 and script_end != -1:
            script_content = content[script_start+8:script_end]
            
            # 修改事件监听器从pdfContainer到signatureLayer
            script_content = re.sub(
                r'pdfContainer\.addEventListener\(\'click\', function\(e\)',
                'signatureLayer.addEventListener(\'click\', function(e)',
                script_content
            )
            
            # 添加阻止事件冒泡的代码
            script_content = re.sub(
                r'(const deleteBtn = document\.createElement\(\'button\'\);.*?deleteBtn\.addEventListener\(\'click\', function\()(\))',
                r'\1e\2\n            e.preventDefault();\n            e.stopPropagation();',
                script_content,
                flags=re.DOTALL
            )
            
            # 修改iframe加载事件
            iframe_load_code = """
        pdfViewer.onload = function() {
            console.log("PDF iframe已加载完成");
            // 确保签名层可交互
            signatureLayer.style.pointerEvents = 'auto';
            // 移除iframe的pointer-events，这样点击事件可以传递到签名层
            pdfViewer.style.pointerEvents = 'none';
            
            console.log("调试信息 - 签名层样式:", {
                pointerEvents: signatureLayer.style.pointerEvents,
                zIndex: signatureLayer.style.zIndex,
                width: signatureLayer.offsetWidth,
                height: signatureLayer.offsetHeight
            });
        };"""
            
            # 替换iframe加载事件
            script_content = re.sub(
                r'pdfViewer\.onload = function\(\) \{.*?\};',
                iframe_load_code,
                script_content,
                flags=re.DOTALL
            )
            
            # 添加触摸设备支持
            touch_support_code = """
    // 添加触摸设备支持
    function addTouchEvents() {
        signatureLayer.addEventListener('touchstart', function(e) {
            console.log("触摸开始事件触发");
            // 如果用户正在拖动签名，则不触发点击事件
            if (isDragging) return;
            
            // 获取第一个触摸点
            const touch = e.touches[0];
            
            // 忽略已有签名上的触摸
            if (touch.target.closest('.signature-item')) {
                console.log("触摸了现有签名，忽略");
                return;
            }
            
            // 计算触摸位置
            const rect = pdfContainer.getBoundingClientRect();
            const x = touch.clientX - rect.left;
            const y = touch.clientY - rect.top;
            
            console.log(`触摸位置: x=${x}, y=${y}`);
            
            // 暂存位置信息
            currentSignature = { x, y };
            
            // 显示签名板
            signatureModal.show();
            
            // 阻止默认行为和冒泡
            e.preventDefault();
        });
        
        // 给签名项添加触摸拖拽支持
        signatureLayer.addEventListener('touchmove', function(e) {
            if (!isDragging || !currentSignature) return;
            
            // 阻止页面滚动
            e.preventDefault();
            
            const touch = e.touches[0];
            const rect = pdfContainer.getBoundingClientRect();
            const x = touch.clientX - rect.left - offsetX;
            const y = touch.clientY - rect.top - offsetY;
            
            // 限制在PDF容器内
            const maxX = rect.width - currentSignature.offsetWidth;
            const maxY = rect.height - currentSignature.offsetHeight;
            const boundedX = Math.max(0, Math.min(x, maxX));
            const boundedY = Math.max(0, Math.min(y, maxY));
            
            currentSignature.style.left = boundedX + 'px';
            currentSignature.style.top = boundedY + 'px';
            
            // 更新数组中的位置
            const index = signatureItems.findIndex(item => item.element === currentSignature);
            if (index !== -1) {
                signatureItems[index].x = boundedX;
                signatureItems[index].y = boundedY;
            }
        });
        
        signatureLayer.addEventListener('touchend', function() {
            if (isDragging) {
                console.log("触摸拖拽结束");
                if (currentSignature) {
                    currentSignature.style.zIndex = '100';
                }
            }
            isDragging = false;
            currentSignature = null;
        });
    }
    
    // 添加触摸事件支持
    if ('ontouchstart' in window) {
        console.log("检测到触摸设备，添加触摸事件支持");
        addTouchEvents();
    }"""
            
            # 在脚本末尾添加触摸支持代码
            script_content = script_content.replace("console.log(\"PDF签名页面初始化完成\");", 
                                                  touch_support_code + "\n    console.log(\"PDF签名页面初始化完成\");")
            
            # 更新脚本内容
            content = content[:script_start+8] + script_content + content[script_end:]
        
        # 保存修改后的文件
        with open(PDF_SIGN_TEMPLATE, 'w', encoding='utf-8') as f:
            f.write(content)
            
        print(f"成功修复PDF签名模板: {PDF_SIGN_TEMPLATE}")
        return True
        
    except Exception as e:
        print(f"修复PDF签名模板时出错: {str(e)}")
        return False

def main():
    """主函数"""
    print("开始修复PDF手写签名功能...")
    
    # 检查模板目录是否存在
    if not os.path.exists(TEMPLATE_DIR):
        print(f"错误: 模板目录不存在 {TEMPLATE_DIR}")
        print("请确保您在正确的项目根目录下运行此脚本")
        return False
    
    # 修复PDF签名模板
    if not fix_pdf_sign_template():
        print("修复PDF签名模板失败")
        return False
    
    print("PDF手写签名功能修复完成!")
    print("请重启Web应用以应用更改")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 