# 文件管理系统备份功能

本文档介绍如何使用和配置文件管理系统的备份功能。

## 功能概述

备份系统能够帮助您：

- 定期备份数据库文件
- 备份关键配置文件
- 自动打包备份为ZIP文件
- 自动清理过期备份

## 手动备份

### 直接执行备份

```bash
python backup_system.py
```

这将执行一次完整备份，使用默认设置。

### 可选参数

```bash
python backup_system.py --no-compress  # 不压缩备份文件
python backup_system.py --no-clean     # 不清理旧备份
python backup_system.py --keep-days 60  # 保留60天的备份（默认30天）
```

## 设置定期备份

### 自动设置（推荐）

```bash
# Linux/macOS
python setup_backup_cron.py

# Windows
python setup_backup_cron.py
```

这将根据您的操作系统自动设置适当的定时任务，默认每天凌晨2:00执行备份。

### 自定义时间

```bash
# Linux/macOS - 自定义cron表达式（每周一凌晨3点）
python setup_backup_cron.py --time "0 3 * * 1"

# Windows - 自定义时间（每天下午6点）
python setup_backup_cron.py --time "18:00"
```

### 使用特定的Python解释器

```bash
python setup_backup_cron.py --python /path/to/python3
```

## 备份文件存储位置

所有备份文件将存储在项目根目录下的`backups`文件夹中：

- ZIP格式的备份文件命名为: `YYYYMMDD_HHMMSS_backup.zip`
- 如果选择不压缩，备份将存储在带时间戳的子目录中

## 备份内容

每次备份包含以下内容：

1. **数据库文件**: `instance/file_manager.db`
2. **配置文件**:
   - `app/config/config.py`
   - `instance/config.py`
   - `requirements.txt`
   - `uwsgi.ini`
   - `start_prod.sh`
   - `stop_prod.sh`

## 日志

备份操作的日志将记录在:
- `logs/backup.log`: 主备份日志
- `logs/backup_cron.log`: 定时任务执行日志

## 手动恢复

如需从备份中恢复，请按照以下步骤操作：

1. 停止文件管理系统服务: `./stop_prod.sh`
2. 解压缩备份ZIP文件（如果是压缩格式）
3. 复制数据库文件: `cp backups/[备份目录]/file_manager.db instance/`
4. 如有需要，恢复配置文件
5. 重新启动服务: `./start_prod.sh`

## 故障排除

如果备份失败，请检查:

1. 权限问题: 确保有足够的权限访问备份的文件
2. 磁盘空间: 确保有足够的磁盘空间
3. 查看日志文件了解详细错误信息

## 安全建议

1. 定期将备份复制到其他物理位置或云存储服务
2. 定期测试备份恢复流程
3. 考虑加密包含敏感信息的备份 