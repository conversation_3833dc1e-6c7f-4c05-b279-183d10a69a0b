#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复电子签名表结构脚本
"""

import os
import sys
import sqlite3
from datetime import datetime
import pytz

# 确保能够导入app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def fix_signature_table():
    """修复Signature表结构"""
    try:
        # 数据库路径 - 默认SQLite数据库路径
        db_path = "instance/file_manager.sqlite"
        print(f"尝试连接数据库: {db_path}")
        
        # 直接连接SQLite数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表是否存在
        print("检查Signature表是否存在...")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='signature'")
        if not cursor.fetchone():
            print("Signature表不存在，将创建新表...")
            cursor.execute("""
            CREATE TABLE signature (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                file_id INTEGER NOT NULL,
                signature_data TEXT NOT NULL,
                signature_date DATETIME,
                signature_metadata TEXT,
                FOREIGN KEY (user_id) REFERENCES user (id),
                FOREIGN KEY (file_id) REFERENCES file (id)
            )
            """)
            conn.commit()
            print("Signature表创建成功!")
            return
        
        # 检查表结构
        print("检查表结构...")
        cursor.execute("PRAGMA table_info(signature)")
        columns = [column[1] for column in cursor.fetchall()]
        print(f"当前表结构: {columns}")
        
        if 'file_id' not in columns:
            print("缺少file_id字段，尝试修复...")
            
            # 方法1: 尝试直接添加列
            try:
                print("尝试方法1: 直接添加列")
                cursor.execute("ALTER TABLE signature ADD COLUMN file_id INTEGER NOT NULL DEFAULT 0")
                conn.commit()
                print("成功添加file_id字段!")
            except Exception as e:
                print(f"使用方法1添加字段失败: {str(e)}")
                conn.rollback()
                
                print("尝试方法2: 重建表...")
                try:
                    # 备份现有数据
                    cursor.execute("CREATE TABLE signature_old AS SELECT * FROM signature")
                    
                    # 删除原表
                    cursor.execute("DROP TABLE signature")
                    
                    # 创建新表
                    cursor.execute("""
                    CREATE TABLE signature (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER NOT NULL,
                        file_id INTEGER NOT NULL,
                        signature_data TEXT NOT NULL,
                        signature_date DATETIME,
                        signature_metadata TEXT,
                        FOREIGN KEY (user_id) REFERENCES user (id),
                        FOREIGN KEY (file_id) REFERENCES file (id)
                    )
                    """)
                    
                    # 复制数据
                    cursor.execute("""
                    INSERT INTO signature (id, user_id, signature_data, signature_date, signature_metadata, file_id)
                    SELECT id, user_id, signature_data, signature_date, signature_metadata, 0
                    FROM signature_old
                    """)
                    
                    conn.commit()
                    print("表重建成功!")
                except Exception as e:
                    print(f"使用方法2重建表失败: {str(e)}")
                    conn.rollback()
                    
                    # 如果方法2失败，尝试方法3
                    print("尝试方法3: 从头创建表...")
                    try:
                        # 删除可能存在的表
                        cursor.execute("DROP TABLE IF EXISTS signature")
                        
                        # 创建新表
                        cursor.execute("""
                        CREATE TABLE signature (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            user_id INTEGER NOT NULL,
                            file_id INTEGER NOT NULL,
                            signature_data TEXT NOT NULL,
                            signature_date DATETIME,
                            signature_metadata TEXT,
                            FOREIGN KEY (user_id) REFERENCES user (id),
                            FOREIGN KEY (file_id) REFERENCES file (id)
                        )
                        """)
                        
                        conn.commit()
                        print("方法3创建新表成功!")
                    except Exception as e:
                        print(f"使用方法3创建表失败: {str(e)}")
                        conn.rollback()
                        raise
        else:
            print("Signature表结构正常，无需修复")
            
        # 查询最终表结构
        cursor.execute("PRAGMA table_info(signature)")
        columns = [column[1] for column in cursor.fetchall()]
        print(f"最终表结构: {columns}")
        
        # 检查UserSignature表是否存在
        print("检查UserSignature表是否存在...")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='user_signature'")
        if not cursor.fetchone():
            print("UserSignature表不存在，将创建新表...")
            cursor.execute("""
            CREATE TABLE user_signature (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                signature_data TEXT NOT NULL,
                created_at DATETIME,
                is_default BOOLEAN DEFAULT 1,
                description TEXT,
                FOREIGN KEY (user_id) REFERENCES user (id)
            )
            """)
            conn.commit()
            print("UserSignature表创建成功!")
        else:
            print("UserSignature表已存在，无需创建")
        
        # 关闭连接
        conn.close()
            
    except Exception as e:
        print(f"修复Signature表时出错: {str(e)}")
        raise

if __name__ == "__main__":
    fix_signature_table()
    print("修复完成!") 