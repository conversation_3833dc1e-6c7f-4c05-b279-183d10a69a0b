{% extends "base.html" %}
{% block content %}
<div class="container-fluid p-0">
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h2 class="card-title">{{ t('Keyword Management') }}</h2>
                    <p class="text-muted">{{ t('You can create global keywords or associate keywords with specific folders') }}</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 左侧：关键字1管理 -->
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="card-title">{{ t('Add Keyword 1') }}</h3>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('main.manage_keywords') }}">
                        <input type="hidden" name="type" value="1">
                        <div class="mb-3">
                            <label for="keyword1" class="form-label">{{ t('Keyword 1') }}</label>
                            <input type="text" class="form-control" id="keyword1" name="keyword" required>
                            <div class="form-text">{{ t('Will appear in the first keyword dropdown when filtering') }}</div>
                        </div>
                        <div class="mb-3">
                            <label for="folder_id1" class="form-label">{{ t('Associated Folder') }}</label>
                            <select class="form-select" id="folder_id1" name="folder_id">
                                <option value="">{{ t('Global (All Folders)') }}</option>
                                {% for folder in folders %}
                                <option value="{{ folder.id }}">{{ folder.name }}</option>
                                {% endfor %}
                            </select>
                            <div class="form-text">{{ t('Choose a folder or keep it global') }}</div>
                        </div>
                        <button type="submit" class="btn btn-primary">{{ t('Add Keyword 1') }}</button>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- 右侧：关键字2管理 -->
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="card-title">{{ t('Add Keyword 2') }}</h3>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('main.manage_keywords') }}">
                        <input type="hidden" name="type" value="2">
                        <div class="mb-3">
                            <label for="keyword2" class="form-label">{{ t('Keyword 2') }}</label>
                            <input type="text" class="form-control" id="keyword2" name="keyword" required>
                            <div class="form-text">{{ t('Will appear in the second keyword dropdown when filtering') }}</div>
                        </div>
                        <div class="mb-3">
                            <label for="folder_id2" class="form-label">{{ t('Associated Folder') }}</label>
                            <select class="form-select" id="folder_id2" name="folder_id">
                                <option value="">{{ t('Global (All Folders)') }}</option>
                                {% for folder in folders %}
                                <option value="{{ folder.id }}">{{ folder.name }}</option>
                                {% endfor %}
                            </select>
                            <div class="form-text">{{ t('Choose a folder or keep it global') }}</div>
                        </div>
                        <button type="submit" class="btn btn-primary">{{ t('Add Keyword 2') }}</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">{{ t('Keyword List') }}</h3>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary" id="showAll">{{ t('All Keywords') }}</button>
                        <button type="button" class="btn btn-outline-primary" id="showKeyword1">{{ t('Keyword 1 Only') }}</button>
                        <button type="button" class="btn btn-outline-primary" id="showKeyword2">{{ t('Keyword 2 Only') }}</button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 过滤器 -->
                    <div class="mb-3">
                        <label for="filter_folder" class="form-label">{{ t('Filter by Folder') }}</label>
                        <select class="form-select" id="filter_folder" onchange="filterKeywords()">
                            <option value="all">{{ t('Show All') }}</option>
                            <option value="global">{{ t('Global Keywords Only') }}</option>
                            {% for folder in folders %}
                            <option value="{{ folder.id }}">{{ folder.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{{ t('Keyword') }}</th>
                                    <th>{{ t('Type') }}</th>
                                    <th>{{ t('Folder') }}</th>
                                    <th>{{ t('Created Date') }}</th>
                                    <th>{{ t('Actions') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for keyword in keywords %}
                                <tr data-folder-id="{{ keyword.folder_id or 'global' }}" data-keyword-type="{{ keyword.keyword_type }}">
                                    <td>{{ keyword.word }}</td>
                                    <td>
                                        {% if keyword.keyword_type == 1 %}
                                        <span class="badge bg-primary">{{ t('Keyword 1') }}</span>
                                        {% elif keyword.keyword_type == 2 %}
                                        <span class="badge bg-success">{{ t('Keyword 2') }}</span>
                                        {% else %}
                                        <span class="badge bg-info">{{ t('General') }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if keyword.folder_id %}
                                        <span class="badge bg-secondary">{{ keyword.folder.name }}</span>
                                        {% else %}
                                        <span class="badge bg-light text-dark">{{ t('Global') }}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ keyword.create_date.strftime('%Y-%m-%d %H:%M') }}</td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-danger" 
                                                onclick="deleteKeyword({{ keyword.id }}, '{{ keyword.word }}')">
                                            {{ t('Delete') }}
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                                {% if not keywords %}
                                <tr>
                                    <td colspan="5" class="text-center">{{ t('No keywords found') }}</td>
                                </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function deleteKeyword(keywordId, word) {
    if (confirm('{{ t("Are you sure you want to delete this keyword?") }}'.replace('{keyword}', word))) {
        fetch(`/admin/keywords/delete/${keywordId}`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.reload();
            } else {
                alert(data.message || '{{ t("Delete failed") }}');
            }
        })
        .catch(error => alert('{{ t("Operation failed") }}'));
    }
}

function filterKeywords() {
    const filterFolder = document.getElementById('filter_folder').value;
    const rows = document.querySelectorAll('tbody tr[data-folder-id]');
    
    rows.forEach(row => {
        const folderId = row.getAttribute('data-folder-id');
        let showByFolder = false;
        
        if (filterFolder === 'all') {
            showByFolder = true;
        } else if (filterFolder === 'global' && folderId === 'global') {
            showByFolder = true;
        } else if (filterFolder === folderId) {
            showByFolder = true;
        }
        
        // 结合类型过滤
        const keywordType = row.getAttribute('data-keyword-type');
        let showByType = true;
        
        if (currentTypeFilter === 'keyword1' && keywordType !== '1') {
            showByType = false;
        } else if (currentTypeFilter === 'keyword2' && keywordType !== '2') {
            showByType = false;
        }
        
        row.style.display = (showByFolder && showByType) ? '' : 'none';
    });
}

// 关键字类型过滤
let currentTypeFilter = 'all';

document.getElementById('showAll').addEventListener('click', function() {
    currentTypeFilter = 'all';
    this.classList.add('active');
    document.getElementById('showKeyword1').classList.remove('active');
    document.getElementById('showKeyword2').classList.remove('active');
    filterKeywords();
});

document.getElementById('showKeyword1').addEventListener('click', function() {
    currentTypeFilter = 'keyword1';
    this.classList.add('active');
    document.getElementById('showAll').classList.remove('active');
    document.getElementById('showKeyword2').classList.remove('active');
    filterKeywords();
});

document.getElementById('showKeyword2').addEventListener('click', function() {
    currentTypeFilter = 'keyword2';
    this.classList.add('active');
    document.getElementById('showAll').classList.remove('active');
    document.getElementById('showKeyword1').classList.remove('active');
    filterKeywords();
});

// 初始标记"全部"按钮为激活状态
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('showAll').classList.add('active');
});
</script>
{% endblock %} 