import os
import secrets
from datetime import datetime, timedelta

class Config:
    # 定义基础路径 - 使用当前目录作为基础路径
    BASE_DIR = os.path.abspath(os.path.dirname(os.path.dirname(__file__)))
    
    SECRET_KEY = 'your-secret-key-here'  # 使用一个强随机密钥
    
    # 数据库路径
    DB_PATH = os.path.join(BASE_DIR, 'instance', 'file_manager.db')
    SQLALCHEMY_DATABASE_URI = f'sqlite:///{DB_PATH}'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # 上传文件路径
    UPLOAD_FOLDER = os.path.join(BASE_DIR, 'uploads')
    MAX_CONTENT_LENGTH = 1600 * 1024 * 1024  # 16MB
    PORT = 2026 
    
    # 日志配置
    LOG_DIR = os.path.join(BASE_DIR, 'logs')
    LOG_FILE = os.path.join(LOG_DIR, 'app.log')
    LOG_FORMAT = '%(asctime)s [%(levelname)s] - %(message)s'
    LOG_LEVEL = 'INFO'

    # 确保必要的目录存在
    REQUIRED_DIRS = [
        os.path.dirname(DB_PATH),  # instance 目录
        UPLOAD_FOLDER,             # uploads 目录
        LOG_DIR                    # logs 目录
    ]
    
    # 创建必要的目录
    for path in REQUIRED_DIRS:
        if path and not os.path.exists(path):
            try:
                os.makedirs(path)
            except Exception as e:
                print(f"创建目录失败 {path}: {str(e)}")

    # 确保日志目录存在
    if not os.path.exists(LOG_DIR):
        os.makedirs(LOG_DIR) 