from app import create_app, db
from app.models import User

app = create_app()

with app.app_context():
    # 创建数据库表
    db.create_all()
    
    # 创建管理员用户
    admin = User(username='cv24051', is_admin=True)
    admin.set_password('admin')
    db.session.add(admin)
    
    # 创建普通用户
    user = User(username='user', is_admin=False)
    user.set_password('user')
    db.session.add(user)
    
    db.session.commit()
    
    print("用户创建成功") 