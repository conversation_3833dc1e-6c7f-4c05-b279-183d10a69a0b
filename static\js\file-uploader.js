/**
 * 文件管理系统分片上传组件
 * 支持大文件分片上传、断点续传和进度显示
 */
class ChunkUploader {
    constructor(options) {
        this.options = {
            url: '/api/chunk-upload',  // 上传接口URL
            chunkSize: 2 * 1024 * 1024,  // 分片大小（默认2MB）
            simultaneousUploads: 3,      // 同时上传的分片数
            maxRetries: 3,               // 最大重试次数
            ...options
        };
        
        // 上传状态
        this.status = {
            uploading: false,
            paused: false,
            completed: false,
            progress: 0,
            fileId: null,
            uploadedChunks: new Set(),
            failedChunks: new Map(),
            totalChunks: 0
        };
        
        // 事件回调
        this.events = {
            onProgress: this.options.onProgress || function() {},
            onSuccess: this.options.onSuccess || function() {},
            onError: this.options.onError || function() {},
            onPaused: this.options.onPaused || function() {},
            onResumed: this.options.onResumed || function() {}
        };
    }
    
    /**
     * 开始上传文件
     * @param {File} file - 要上传的文件对象
     * @param {number|null} folderId - 目标文件夹ID，null表示根目录
     */
    upload(file, folderId = null) {
        if (this.status.uploading) {
            console.warn('已有上传任务正在进行中');
            return false;
        }
        
        // 重置状态
        this.status = {
            uploading: true,
            paused: false,
            completed: false,
            progress: 0,
            fileId: this._generateFileId(file),
            uploadedChunks: new Set(),
            failedChunks: new Map(),
            totalChunks: Math.ceil(file.size / this.options.chunkSize)
        };
        
        this.file = file;
        this.folderId = folderId;
        
        console.log(`开始上传文件: ${file.name}, 大小: ${this._formatSize(file.size)}, 分片数: ${this.status.totalChunks}`);
        
        // 开始上传分片
        this._uploadChunks();
        
        return true;
    }
    
    /**
     * 暂停上传
     */
    pause() {
        if (!this.status.uploading || this.status.completed) {
            return false;
        }
        
        this.status.paused = true;
        this.events.onPaused(this.status);
        console.log('已暂停上传');
        
        return true;
    }
    
    /**
     * 恢复上传
     */
    resume() {
        if (!this.status.paused || this.status.completed) {
            return false;
        }
        
        this.status.paused = false;
        this.events.onResumed(this.status);
        console.log('已恢复上传');
        
        // 继续上传剩余分片
        this._uploadChunks();
        
        return true;
    }
    
    /**
     * 取消上传
     */
    cancel() {
        this.status.uploading = false;
        this.status.paused = false;
        console.log('已取消上传');
        
        return true;
    }
    
    /**
     * 上传所有分片
     * @private
     */
    _uploadChunks() {
        // 如果暂停或已完成，不继续上传
        if (this.status.paused || !this.status.uploading || this.status.completed) {
            return;
        }
        
        const pendingChunks = [];
        
        // 确定需要上传的分片
        for (let i = 0; i < this.status.totalChunks; i++) {
            if (!this.status.uploadedChunks.has(i)) {
                pendingChunks.push(i);
            }
        }
        
        if (pendingChunks.length === 0) {
            // 所有分片已上传完成
            this._completeUpload();
            return;
        }
        
        // 同时上传多个分片
        const activeUploads = Math.min(this.options.simultaneousUploads, pendingChunks.length);
        for (let i = 0; i < activeUploads; i++) {
            this._uploadChunk(pendingChunks[i]);
        }
    }
    
    /**
     * 上传单个分片
     * @param {number} chunkNumber - 分片序号
     * @private
     */
    _uploadChunk(chunkNumber) {
        // 如果暂停或已完成，不继续上传
        if (this.status.paused || !this.status.uploading || this.status.completed) {
            return;
        }
        
        // 如果此分片已上传成功，跳过
        if (this.status.uploadedChunks.has(chunkNumber)) {
            // 继续上传下一个分片
            this._checkAndContinue();
            return;
        }
        
        // 切分文件为分片
        const start = chunkNumber * this.options.chunkSize;
        const end = Math.min(start + this.options.chunkSize, this.file.size);
        const chunk = this.file.slice(start, end);
        
        // 创建表单数据
        const formData = new FormData();
        formData.append('file', chunk);
        formData.append('chunkNumber', chunkNumber);
        formData.append('totalChunks', this.status.totalChunks);
        formData.append('fileId', this.status.fileId);
        formData.append('filename', this.file.name);
        if (this.folderId !== null) {
            formData.append('folderId', this.folderId);
        }
        
        // 发送请求
        fetch(this.options.url, {
            method: 'POST',
            body: formData,
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                // 上传成功
                this.status.uploadedChunks.add(chunkNumber);
                this.status.failedChunks.delete(chunkNumber);
                
                // 更新进度
                this._updateProgress();
                
                // 继续上传下一个分片
                this._checkAndContinue();
                
                // 检查是否为最后一个分片
                if (result.fileId && chunkNumber === this.status.totalChunks - 1) {
                    this._finalizeUpload(result.fileId);
                }
            } else {
                // 上传失败
                this._handleChunkError(chunkNumber, new Error(result.message || '上传失败'));
            }
        })
        .catch(error => {
            // 网络错误
            this._handleChunkError(chunkNumber, error);
        });
    }
    
    /**
     * 处理分片上传错误
     * @param {number} chunkNumber - 分片序号 
     * @param {Error} error - 错误对象
     * @private
     */
    _handleChunkError(chunkNumber, error) {
        console.error(`分片 ${chunkNumber} 上传失败:`, error);
        
        // 记录失败次数
        const retries = this.status.failedChunks.get(chunkNumber) || 0;
        
        if (retries < this.options.maxRetries) {
            // 尝试重试
            this.status.failedChunks.set(chunkNumber, retries + 1);
            console.log(`准备重试分片 ${chunkNumber}, 第 ${retries + 1} 次尝试`);
            
            // 延迟重试
            setTimeout(() => {
                if (this.status.uploading && !this.status.paused) {
                    this._uploadChunk(chunkNumber);
                }
            }, 1000 * Math.pow(2, retries)); // 指数退避策略
        } else {
            // 超过最大重试次数，通知错误
            this.events.onError({
                chunkNumber,
                error,
                message: `分片 ${chunkNumber} 上传失败，已超过最大重试次数`
            });
        }
    }
    
    /**
     * 检查并继续上传其他分片
     * @private
     */
    _checkAndContinue() {
        if (!this.status.uploading || this.status.paused) {
            return;
        }
        
        // 查找第一个未上传的分片
        for (let i = 0; i < this.status.totalChunks; i++) {
            if (!this.status.uploadedChunks.has(i) && 
                (!this.status.failedChunks.has(i) || 
                 this.status.failedChunks.get(i) < this.options.maxRetries)) {
                // 找到了未上传的分片，开始上传
                this._uploadChunk(i);
                return;
            }
        }
        
        // 没有找到未上传的分片，检查是否全部完成
        if (this.status.uploadedChunks.size === this.status.totalChunks) {
            this._completeUpload();
        }
    }
    
    /**
     * 完成上传过程
     * @private
     */
    _completeUpload() {
        if (this.status.completed) {
            return;
        }
        
        this.status.completed = true;
        this.status.progress = 100;
        this.status.uploading = false;
        
        console.log('所有分片上传完成');
        this.events.onProgress(100);
    }
    
    /**
     * 最终化上传（处理服务器返回的文件ID）
     * @param {string|number} fileId - 服务器返回的文件ID
     * @private
     */
    _finalizeUpload(fileId) {
        console.log('文件上传成功，服务器ID:', fileId);
        this.events.onSuccess({
            fileId: fileId,
            filename: this.file.name,
            size: this.file.size
        });
    }
    
    /**
     * 更新上传进度
     * @private
     */
    _updateProgress() {
        const progress = Math.floor((this.status.uploadedChunks.size / this.status.totalChunks) * 100);
        this.status.progress = progress;
        this.events.onProgress(progress);
    }
    
    /**
     * 生成唯一的文件ID
     * @param {File} file - 文件对象
     * @returns {string} 唯一ID
     * @private
     */
    _generateFileId(file) {
        return `${Date.now()}-${Math.random().toString(36).substring(2, 15)}-${file.name.replace(/[^a-zA-Z0-9]/g, '')}`;
    }
    
    /**
     * 格式化文件大小显示
     * @param {number} bytes - 字节数
     * @returns {string} 格式化后的大小
     * @private
     */
    _formatSize(bytes) {
        if (bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

// 使用示例
/*
const uploader = new ChunkUploader({
    onProgress: (progress) => {
        console.log(`上传进度: ${progress}%`);
        document.querySelector('#progressBar').style.width = `${progress}%`;
    },
    onSuccess: (result) => {
        console.log('上传成功:', result);
        alert('文件上传成功!');
    },
    onError: (error) => {
        console.error('上传失败:', error);
        alert(`上传失败: ${error.message}`);
    }
});

// 上传按钮点击事件
document.querySelector('#uploadBtn').addEventListener('click', () => {
    const fileInput = document.querySelector('#fileInput');
    if (fileInput.files.length > 0) {
        const file = fileInput.files[0];
        const folderId = document.querySelector('#folderSelect').value;
        uploader.upload(file, folderId === '' ? null : parseInt(folderId));
    }
});

// 暂停按钮
document.querySelector('#pauseBtn').addEventListener('click', () => {
    uploader.pause();
});

// 恢复按钮
document.querySelector('#resumeBtn').addEventListener('click', () => {
    uploader.resume();
});
*/ 