#!/bin/bash

# 检查服务状态

echo "=== 文件管理系统状态检查 ==="
echo "当前时间: $(date)"
echo ""

# 检查进程
echo "进程状态:"
if pgrep -f "gunicorn -w 4 -b 0.0.0.0:2026 run:app" > /dev/null; then
    echo "✅ 服务正在运行"
    ps -f -o pid,user,%cpu,%mem,start,time,command $(pgrep -f "gunicorn -w 4 -b 0.0.0.0:2026 run:app")
else
    echo "❌ 服务未运行"
fi

echo ""

# 检查端口
echo "端口状态:"
if command -v ss >/dev/null; then
    if ss -tlnp 2>/dev/null | grep -q ":2026 "; then
        echo "✅ 端口2026正在监听"
        ss -tlnp | grep ":2026 "
    else
        echo "❌ 端口2026未监听"
    fi
elif command -v netstat >/dev/null; then
    if netstat -tlnp 2>/dev/null | grep -q ":2026 "; then
        echo "✅ 端口2026正在监听"
        netstat -tlnp | grep ":2026 "
    else
        echo "❌ 端口2026未监听"
    fi
else
    echo "无法检查端口状态（需要ss或netstat命令）"
fi

echo ""

# 检查日志
echo "最近的日志:"
if [ -f "logs/gunicorn.log" ]; then
    echo "最后10行日志:"
    tail -n 10 logs/gunicorn.log
else
    echo "未找到日志文件: logs/gunicorn.log"
fi

echo ""
echo "使用以下命令启动服务: bash start_prod.sh"
echo "使用以下命令停止服务: bash stop_prod.sh" 