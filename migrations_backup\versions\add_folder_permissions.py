"""Add folder permissions

Revision ID: add_folder_permissions
Revises: 47bfdfb21fbb
Create Date: 2024-03-xx xx:xx:xx.xxx

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'add_folder_permissions'
down_revision = '47bfdfb21fbb'  # 确保这是正确的前一个版本
branch_labels = None
depends_on = None

def upgrade():
    # 添加新的权限字段
    op.add_column('folder', sa.Column('is_public', sa.<PERSON>(), nullable=True, server_default='0'))
    op.add_column('folder', sa.Column('allowed_users', sa.String(500), nullable=True, server_default=''))
    op.add_column('folder', sa.Column('read_only', sa.<PERSON>(), nullable=True, server_default='0'))

def downgrade():
    # 删除添加的字段
    op.drop_column('folder', 'read_only')
    op.drop_column('folder', 'allowed_users')
    op.drop_column('folder', 'is_public') 