import os
import sys
import logging
from logging.handlers import RotatingFileHandler

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.dirname(os.path.dirname(__file__))))

from flask import Flask, request, session
from flask_sqlalchemy import SQLAlchemy
from flask_login import <PERSON>ginManager
from flask_migrate import Migrate
from flask_session import Session
from app.config.config import Config

db = SQLAlchemy()
migrate = Migrate()
login_manager = LoginManager()
flask_session = Session()

def create_app(config_class=Config):
    app = Flask(__name__,
                template_folder=os.path.join(os.path.dirname(__file__), 'templates'),
                static_folder=os.path.join(os.path.dirname(os.path.dirname(__file__)), 'static'))
    
    app.config.from_object(config_class)
    
    # 添加多语言支持配置
    app.config['SUPPORTED_LANGUAGES'] = ['zh', 'en']
    app.config['DEFAULT_LANGUAGE'] = 'zh'
    
    # 配置Flask-Session
    app.config['SESSION_TYPE'] = 'filesystem'
    app.config['SESSION_PERMANENT'] = False
    app.config['SESSION_USE_SIGNER'] = True
    app.config['SESSION_FILE_DIR'] = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'flask_session')

    # 配置日志
    if not app.debug and not app.testing:
        # 创建日志处理器
        file_handler = RotatingFileHandler(
            app.config['LOG_FILE'],
            maxBytes=10240000,  # 10MB
            backupCount=10,
            encoding='utf-8'
        )
        
        # 设置日志格式
        file_handler.setFormatter(logging.Formatter(app.config['LOG_FORMAT']))
        
        # 设置日志级别
        file_handler.setLevel(logging.INFO)
        
        # 添加到应用
        app.logger.addHandler(file_handler)
        app.logger.setLevel(logging.INFO)
        app.logger.info('File Manager startup')
    
    db.init_app(app)
    migrate.init_app(app, db)
    flask_session.init_app(app)
    
    # 登录管理器配置
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = '请先登录'
    login_manager.login_message_category = 'info'
    login_manager.session_protection = 'strong'
    
    # 在每个请求前设置语言
    @app.before_request
    def before_request():
        try:
            # 检查session中是否存在language键
            if not session.get('language'):
                # 从浏览器Accept-Language头部获取默认语言
                browser_lang = request.accept_languages.best_match(app.config['SUPPORTED_LANGUAGES'])
                session['language'] = browser_lang or app.config['DEFAULT_LANGUAGE']
                session.modified = True
        except Exception as e:
            app.logger.error(f"设置语言时出错: {str(e)}")
            # 默认使用中文
            session['language'] = app.config['DEFAULT_LANGUAGE']
            session.modified = True

    from app.routes import main, auth
    app.register_blueprint(main)
    app.register_blueprint(auth)

    with app.app_context():
        db.create_all()

    return app 