# PDF手写签名功能修复指南

本文档提供了修复PDF手写签名功能的详细步骤和说明。

## 问题描述

当前PDF手写签名功能存在以下问题：
- 无法在PDF上点击添加签名
- 放置的签名无法移动或删除
- 移动设备上可能无法正常使用

## 修复方法

### 方法一：使用自动修复脚本

我们提供了一个自动修复脚本，可以自动应用所有必要的修改。

#### 在Ubuntu服务器上运行

1. 将`fix_signature_ui.py`脚本上传到项目根目录
2. 赋予脚本执行权限：
   ```bash
   chmod +x fix_signature_ui.py
   ```
3. 运行脚本：
   ```bash
   ./fix_signature_ui.py
   ```
4. 重启Web应用程序：
   ```bash
   # 如果使用systemd
   sudo systemctl restart your-app-service
   
   # 或者如果使用supervisor
   sudo supervisorctl restart your-app-process
   ```

#### 注意事项

- 脚本会自动备份修改前的文件到`backups`目录
- 如果修复失败，可以从备份恢复

### 方法二：手动修改

如果自动脚本无法正常工作，您可以按照以下步骤手动修改：

1. 修改`app/templates/preview/pdf_sign.html`文件：

   a. 在PDF容器部分添加z-index：
   ```html
   <iframe id="pdfViewer" src="{{ file_url }}" style="width: 100%; height: 100%; position: absolute; top: 0; left: 0; border: none; z-index: 1;"></iframe>
   <div id="signatureLayer" class="position-absolute top-0 start-0 w-100 h-100" style="pointer-events: none; z-index: 2;"></div>
   ```

   b. 修改CSS样式，在`<style>`标签中添加：
   ```css
   #signatureLayer {
       z-index: 50;
       pointer-events: auto !important;
   }
   
   #pdfViewer {
       pointer-events: none;
   }
   
   .signature-item {
       pointer-events: auto !important;
   }
   ```

   c. 修改JavaScript事件监听器：
   ```javascript
   // 将事件监听器从pdfContainer改为signatureLayer
   signatureLayer.addEventListener('click', function(e) {
       // 事件处理代码
   });
   ```

   d. 在iframe加载完成后设置正确的pointer-events：
   ```javascript
   pdfViewer.onload = function() {
       signatureLayer.style.pointerEvents = 'auto';
       pdfViewer.style.pointerEvents = 'none';
   };
   ```

## 验证修复

修复完成后，请验证以下功能：

1. 能否点击PDF任意位置打开签名模态框
2. 能否添加签名到PDF上
3. 能否移动已添加的签名
4. 能否删除已添加的签名
5. 在移动设备上测试以上功能

## 常见问题

### 仍然无法点击PDF添加签名

检查浏览器控制台是否有JavaScript错误，确保：
- 所有必要的JavaScript库已正确加载
- 签名层的z-index值大于iframe的z-index值
- 签名层的pointer-events设置为auto

### 签名无法保存

检查服务器日志中是否有错误信息，确保：
- 服务器有足够的磁盘空间
- 应用程序有权限写入文件
- 数据库连接正常

### 移动设备上无法使用

确保添加了触摸事件支持，并且正确处理了触摸事件。

## 联系支持

如有任何问题，请联系技术支持团队。 