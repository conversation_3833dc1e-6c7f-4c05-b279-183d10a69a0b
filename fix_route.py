#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
自动修复routes.py中的pdf_handwriting_signature函数
"""

import os
import sys
import re

def fix_routes():
    routes_file = 'app/routes.py'
    try:
        print(f"读取 {routes_file} 文件...")
        
        # 读取routes.py文件内容
        with open(routes_file, 'r', encoding='utf-8') as f:
            content = f.readlines()
        
        # 查找pdf_handwriting_signature函数开始的行
        start_line = -1
        end_line = -1
        
        for i, line in enumerate(content):
            if "@main.route('/file/pdf_signature/<int:file_id>')" in line:
                start_line = i
                break
        
        if start_line == -1:
            print("未找到pdf_handwriting_signature函数，将添加新函数...")
            # 在文件末尾添加正确的函数
            correct_function = """
# 在PDF上直接手写签名
@main.route('/file/pdf_signature/<int:file_id>')
@login_required
def pdf_handwriting_signature(file_id):
    try:
        file = File.query.get_or_404(file_id)
        
        # 检查权限
        if not is_super_admin(current_user):
            if not file.folder or not file.folder.can_access(current_user):
                flash('您没有权限为此文件添加签名')
                return redirect(url_for('main.index'))
                
        # 检查文件类型是否是PDF
        if file.file_type != 'application/pdf':
            flash('手写签名功能仅支持PDF文件')
            return redirect(url_for('main.preview_file', file_id=file_id))
        
        # 构建相对路径用于模板
        static_folder = current_app.static_folder
        upload_folder = current_app.config['UPLOAD_FOLDER']
        
        # 检查文件是否在static目录下
        file_path = os.path.join(upload_folder, file.path, file.filename)
        if file_path.startswith(static_folder):
            relative_path = file_path[len(static_folder):].lstrip(os.path.sep)
            file_url = url_for('static', filename=relative_path)
        else:
            # 如果不在static目录下，使用流式传输方式
            file_url = url_for('main.stream_file', file_id=file.id)
        
        # 获取用户的默认签名
        try:
            user_signatures = UserSignature.query.filter_by(user_id=current_user.id).all()
        except Exception:
            # 如果查询失败，返回空列表
            user_signatures = []
        
        # 记录操作日志
        log_user_action(
            current_user, 
            'VIEW_PDF_SIGNATURE', 
            f'查看PDF手写签名页面: {file.original_filename}',
            file.id,
            'file'
        )
        
        return render_template('preview/pdf_sign.html', 
                              filename=file.original_filename,
                              file_url=file_url,
                              file_id=file.id,
                              user_signatures=user_signatures)
    except Exception as e:
        current_app.logger.error(f'访问PDF手写签名页面失败: {str(e)}')
        flash('访问PDF手写签名页面失败')
        return redirect(url_for('main.index'))
"""
            with open(routes_file, 'a', encoding='utf-8') as f:
                f.write(correct_function)
            print("已成功添加pdf_handwriting_signature函数！")
            
        else:
            # 如果找到了函数开始行，查找函数结束行
            # 我们假设函数结束于下一个路由装饰器开始前
            for i in range(start_line + 1, len(content)):
                if "@main.route" in content[i] or i == len(content) - 1:
                    end_line = i - 1
                    break
            
            if end_line == -1:
                end_line = len(content) - 1
            
            print(f"已找到pdf_handwriting_signature函数，从第{start_line+1}行到第{end_line+1}行。")
            
            # 创建备份
            backup_file = routes_file + '.bak'
            print(f"创建备份文件：{backup_file}")
            with open(backup_file, 'w', encoding='utf-8') as f:
                f.writelines(content)
            
            # 正确的函数实现
            correct_function_lines = """@main.route('/file/pdf_signature/<int:file_id>')
@login_required
def pdf_handwriting_signature(file_id):
    try:
        file = File.query.get_or_404(file_id)
        
        # 检查权限
        if not is_super_admin(current_user):
            if not file.folder or not file.folder.can_access(current_user):
                flash('您没有权限为此文件添加签名')
                return redirect(url_for('main.index'))
                
        # 检查文件类型是否是PDF
        if file.file_type != 'application/pdf':
            flash('手写签名功能仅支持PDF文件')
            return redirect(url_for('main.preview_file', file_id=file_id))
        
        # 构建相对路径用于模板
        static_folder = current_app.static_folder
        upload_folder = current_app.config['UPLOAD_FOLDER']
        
        # 检查文件是否在static目录下
        file_path = os.path.join(upload_folder, file.path, file.filename)
        if file_path.startswith(static_folder):
            relative_path = file_path[len(static_folder):].lstrip(os.path.sep)
            file_url = url_for('static', filename=relative_path)
        else:
            # 如果不在static目录下，使用流式传输方式
            file_url = url_for('main.stream_file', file_id=file.id)
        
        # 获取用户的默认签名
        try:
            user_signatures = UserSignature.query.filter_by(user_id=current_user.id).all()
        except Exception:
            # 如果查询失败，返回空列表
            user_signatures = []
        
        # 记录操作日志
        log_user_action(
            current_user, 
            'VIEW_PDF_SIGNATURE', 
            f'查看PDF手写签名页面: {file.original_filename}',
            file.id,
            'file'
        )
        
        return render_template('preview/pdf_sign.html', 
                              filename=file.original_filename,
                              file_url=file_url,
                              file_id=file.id,
                              user_signatures=user_signatures)
    except Exception as e:
        current_app.logger.error(f'访问PDF手写签名页面失败: {str(e)}')
        flash('访问PDF手写签名页面失败')
        return redirect(url_for('main.index'))
""".split('\n')
            
            # 替换旧函数
            new_content = content[:start_line] + [line + '\n' for line in correct_function_lines] + content[end_line+1:]
            
            # 写入文件
            with open(routes_file, 'w', encoding='utf-8') as f:
                f.writelines(new_content)
                
            print("已成功修复pdf_handwriting_signature函数！")
        
        return True
        
    except Exception as e:
        print(f"修复过程中出错: {str(e)}")
        return False

if __name__ == "__main__":
    print("开始修复routes.py文件...")
    success = fix_routes()
    if success:
        print("修复完成！请重新启动应用程序以应用更改。")
    else:
        print("修复失败。请手动编辑routes.py文件。") 