import os
import sqlite3
import json
from datetime import datetime
import pytz
from app import create_app, db
from app.models import User, Folder, File, UserGroup, Keyword, UserLog

def datetime_converter(timestamp):
    """转换SQLite中的时间戳到Python datetime对象"""
    if timestamp:
        try:
            dt = datetime.fromisoformat(timestamp)
            return dt.replace(tzinfo=pytz.timezone('Asia/Shanghai'))
        except ValueError:
            return None
    return None

def migrate_data():
    """将数据从SQLite迁移到新数据库"""
    app = create_app()
    
    # 获取SQLite数据库文件路径
    base_dir = os.path.abspath(os.path.dirname(__file__))
    sqlite_path = os.path.join(base_dir, 'instance', 'file_manager.db')
    
    if not os.path.exists(sqlite_path):
        print(f"SQLite数据库文件不存在: {sqlite_path}")
        return
    
    print(f"从SQLite数据库迁移: {sqlite_path}")
    
    # 连接SQLite数据库
    sqlite_conn = sqlite3.connect(sqlite_path)
    sqlite_conn.row_factory = sqlite3.Row
    
    with app.app_context():
        # 清空目标数据库中的所有表
        db.drop_all()
        db.create_all()
        
        # 1. 迁移用户表
        print("迁移用户数据...")
        users = {}  # 用于保存ID映射 {old_id: user_obj}
        
        cursor = sqlite_conn.cursor()
        cursor.execute("SELECT * FROM user")
        for row in cursor.fetchall():
            user = User(
                id=row['id'],
                username=row['username'],
                password_hash=row['password_hash'],
                is_admin=bool(row['is_admin']),
                security_question=row['security_question'],
                security_answer_hash=row['security_answer_hash']
            )
            db.session.add(user)
            users[row['id']] = user
        
        # 2. 迁移用户组
        print("迁移用户组数据...")
        groups = {}  # 用于保存ID映射
        
        cursor.execute("SELECT * FROM user_group")
        for row in cursor.fetchall():
            group = UserGroup(
                id=row['id'],
                name=row['name'],
                description=row['description'],
                created_by=row['created_by'],
                create_date=datetime_converter(row['create_date']),
                can_upload=bool(row['can_upload']),
                can_download=bool(row['can_download']),
                can_delete=bool(row['can_delete']),
                can_share=bool(row['can_share']),
                storage_limit=row['storage_limit'],
                is_admin_group=bool(row['is_admin_group'])
            )
            db.session.add(group)
            groups[row['id']] = group
            
        # 3. 迁移用户-组关系
        print("迁移用户-组关系...")
        cursor.execute("SELECT * FROM user_group_members")
        for row in cursor.fetchall():
            user_id = row['user_id']
            group_id = row['group_id']
            
            if user_id in users and group_id in groups:
                groups[group_id].members.append(users[user_id])
        
        # 4. 迁移文件夹
        print("迁移文件夹数据...")
        folders = {}  # 用于保存ID映射
        
        cursor.execute("SELECT * FROM folder")
        for row in cursor.fetchall():
            folder = Folder(
                id=row['id'],
                name=row['name'],
                path=row['path'],
                parent_id=row['parent_id'],
                user_id=row['user_id'],
                create_date=datetime_converter(row['create_date']),
                tags=row['tags'],
                is_public=bool(row['is_public']),
                allowed_users=row['allowed_users'],
                allowed_groups=row['allowed_groups'],
                read_only=bool(row['read_only']),
                is_deleted=bool(row['is_deleted']),
                delete_time=datetime_converter(row['delete_time'])
            )
            db.session.add(folder)
            folders[row['id']] = folder
            
        # 5. 迁移文件
        print("迁移文件数据...")
        files = {}  # 用于保存ID映射
        
        cursor.execute("SELECT * FROM file")
        for row in cursor.fetchall():
            file = File(
                id=row['id'],
                filename=row['filename'],
                original_filename=row['original_filename'],
                file_type=row['file_type'],
                file_size=row['file_size'],
                upload_date=datetime_converter(row['upload_date']),
                tags=row['tags'],
                path=row['path'],
                folder_id=row['folder_id'],
                user_id=row['user_id'],
                is_deleted=bool(row['is_deleted']),
                delete_time=datetime_converter(row['delete_time'])
            )
            db.session.add(file)
            files[row['id']] = file
            
        # 6. 迁移关键词
        print("迁移关键词数据...")
        cursor.execute("SELECT * FROM keyword")
        for row in cursor.fetchall():
            keyword = Keyword(
                id=row['id'],
                word=row['word'],
                created_by=row['created_by'],
                create_date=datetime_converter(row['create_date']),
                folder_id=row['folder_id'],
                keyword_type=row['keyword_type']
            )
            db.session.add(keyword)
            
        # 7. 迁移用户日志
        print("迁移用户日志数据...")
        cursor.execute("SELECT * FROM user_log")
        for row in cursor.fetchall():
            log = UserLog(
                id=row['id'],
                user_id=row['user_id'],
                action=row['action'],
                details=row['details'],
                timestamp=datetime_converter(row['timestamp']),
                ip_address=row['ip_address'],
                target_id=row['target_id'],
                target_type=row['target_type']
            )
            db.session.add(log)
            
        # 提交所有的更改
        db.session.commit()
        print("数据迁移完成！")
    
    # 关闭SQLite连接
    sqlite_conn.close()

if __name__ == '__main__':
    migrate_data() 