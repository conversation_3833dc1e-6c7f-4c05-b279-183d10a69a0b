#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
添加电子签名表迁移脚本
"""

import os
import sys
from datetime import datetime
import pytz

# 确保能够导入app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app, db
from app.models import Signature

app = create_app()

def add_signature_table():
    """添加Signature表"""
    with app.app_context():
        try:
            # 检查表是否已存在
            print("检查Signature表是否存在...")
            inspector = db.inspect(db.engine)
            tables = inspector.get_table_names()
            
            if 'signature' in tables:
                print("Signature表已存在，无需创建")
                return
            
            # 创建表
            print("创建Signature表...")
            db.create_all()
            
            # 提交更改
            db.session.commit()
            print("Signature表创建成功!")
            
        except Exception as e:
            print(f"创建Signature表时出错: {str(e)}")
            db.session.rollback()
            raise

if __name__ == "__main__":
    add_signature_table()
    print("迁移完成!") 