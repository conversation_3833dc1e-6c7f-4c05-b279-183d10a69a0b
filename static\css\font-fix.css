/* Font Awesome字体路径修复 */

@font-face {
  font-family: "Font Awesome 6 Free";
  font-style: normal;
  font-weight: 900;
  font-display: block;
  src: url("../webfonts/fa-solid-900.woff2") format("woff2"),
       url("../webfonts/fa-solid-900.ttf") format("truetype");
}

@font-face {
  font-family: "Font Awesome 6 Free";
  font-style: normal;
  font-weight: 400;
  font-display: block;
  src: url("../webfonts/fa-regular-400.woff2") format("woff2"),
       url("../webfonts/fa-regular-400.ttf") format("truetype");
}

@font-face {
  font-family: "Font Awesome 6 Brands";
  font-style: normal;
  font-weight: 400;
  font-display: block;
  src: url("../webfonts/fa-brands-400.woff2") format("woff2"),
       url("../webfonts/fa-brands-400.ttf") format("truetype");
}

.fa,
.fas {
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
}

.far {
  font-family: "Font Awesome 6 Free";
  font-weight: 400;
}

.fab {
  font-family: "Font Awesome 6 Brands";
  font-weight: 400;
} 