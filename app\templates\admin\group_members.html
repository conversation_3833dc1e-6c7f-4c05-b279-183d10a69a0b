{% extends "base.html" %}

{% block content %}
<div class="container">
    <h2 class="mb-4">{{ t('Group Members') }} - {{ group.name }}</h2>
    
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">{{ t('Manage Members') }}</h5>
                        <button class="btn btn-primary btn-sm" onclick="saveMembers()">
                            <i class="fas fa-save"></i> {{ t('Save Changes') }}
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 40px;">
                                        <input type="checkbox" class="form-check-input" id="selectAll" onchange="toggleAll(this)">
                                    </th>
                                    <th>{{ t('Username') }}</th>
                                    <th>{{ t('Email') }}</th>
                                    <th>{{ t('Role') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in users %}
                                <tr>
                                    <td>
                                        <input type="checkbox" class="form-check-input user-checkbox" 
                                               value="{{ user.id }}"
                                               {% if user in group.users %}checked{% endif %}>
                                    </td>
                                    <td>{{ user.username }}</td>
                                    <td>{{ user.email }}</td>
                                    <td>
                                        {% if user.is_super_admin %}
                                        <span class="badge bg-danger">{{ t('Super Admin') }}</span>
                                        {% elif user.is_admin %}
                                        <span class="badge bg-warning">{{ t('Admin') }}</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{{ t('User') }}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleAll(checkbox) {
    document.querySelectorAll('.user-checkbox').forEach(item => {
        item.checked = checkbox.checked;
    });
}

function saveMembers() {
    const selectedUsers = Array.from(document.querySelectorAll('.user-checkbox:checked')).map(cb => cb.value);
    
    fetch('/admin/groups/{{ group.id }}/members/update', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'user_ids[]=' + selectedUsers.join('&user_ids[]=')
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('{{ t("Changes saved successfully") }}');
            location.reload();
        } else {
            alert(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('{{ t("An error occurred") }}');
    });
}
</script>
{% endblock %} 