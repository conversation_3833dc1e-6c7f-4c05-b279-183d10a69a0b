"""Add folder permissions

Revision ID: 5ad0d9a98121
Revises: 47bfdfb21fbb
Create Date: 2025-02-20 19:24:31.873075

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '5ad0d9a98121'
down_revision = '47bfdfb21fbb'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('file', schema=None) as batch_op:
        batch_op.add_column(sa.Column('is_public', sa.Boolean(), nullable=True))
        batch_op.add_column(sa.Column('allowed_users', sa.String(length=1000), nullable=True))

    with op.batch_alter_table('folder', schema=None) as batch_op:
        batch_op.add_column(sa.Column('is_public', sa.Bo<PERSON>an(), nullable=True))
        batch_op.add_column(sa.Column('allowed_users', sa.String(length=1000), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('folder', schema=None) as batch_op:
        batch_op.drop_column('allowed_users')
        batch_op.drop_column('is_public')

    with op.batch_alter_table('file', schema=None) as batch_op:
        batch_op.drop_column('allowed_users')
        batch_op.drop_column('is_public')

    # ### end Alembic commands ###
