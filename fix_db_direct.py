#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
直接使用应用程序配置的数据库连接来修复签名表结构
"""

import os
import sys
import traceback

# 确保能够导入app
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def fix_signature_table():
    """直接修复签名表结构"""
    try:
        print("正在导入应用程序...")
        from app import create_app, db
        from app.models import Signature, UserSignature
        from sqlalchemy import inspect, text

        print("正在创建应用实例...")
        app = create_app()

        with app.app_context():
            print("连接数据库...")
            engine = db.engine
            connection = engine.connect()
            inspector = inspect(engine)
            
            # 获取当前数据库名称
            result = connection.execute(text("SELECT DATABASE()"))
            db_name = result.scalar()
            print(f"当前数据库: {db_name}")
            
            # 检查signature表是否存在
            tables = inspector.get_table_names()
            print(f"数据库表: {tables}")
            
            if 'signature' in tables:
                # 查看当前表结构
                columns = [column['name'] for column in inspector.get_columns('signature')]
                print(f"signature表的当前结构: {columns}")
                
                missing_columns = []
                expected_columns = ['id', 'user_id', 'file_id', 'signature_data', 'signature_date', 'signature_metadata']
                
                for column in expected_columns:
                    if column not in columns:
                        missing_columns.append(column)
                
                if missing_columns:
                    print(f"缺少的列: {missing_columns}")
                    
                    # 删除旧表并重建
                    print("删除旧的signature表...")
                    connection.execute(text("SET FOREIGN_KEY_CHECKS = 0"))
                    connection.execute(text("DROP TABLE IF EXISTS signature"))
                    
                    # 使用SQLAlchemy创建新表
                    print("创建新的signature表...")
                    db.metadata.tables['signature'].create(engine)
                    print("signature表已重建")
                    
                    connection.execute(text("SET FOREIGN_KEY_CHECKS = 1"))
                else:
                    print("signature表结构正确")
            else:
                # 表不存在，创建它
                print("signature表不存在，创建新表...")
                db.metadata.tables['signature'].create(engine)
                print("signature表已创建")
            
            # 检查user_signature表
            if 'user_signature' not in tables:
                print("创建user_signature表...")
                db.metadata.tables['user_signature'].create(engine)
                print("user_signature表已创建")
            else:
                print("user_signature表已存在")
            
            print("所有表结构已修复")
            
    except Exception as e:
        print(f"修复过程中出错: {str(e)}")
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = fix_signature_table()
    if success:
        print("数据库修复成功!")
    else:
        print("数据库修复失败，请查看错误信息")
        sys.exit(1) 