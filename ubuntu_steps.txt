# Ubuntu部署手写签名功能步骤

## 1. 安装必要的依赖项

```bash
sudo apt-get update
sudo apt-get install -y python3-pip
pip install img2pdf pillow
```

## 2. 文件修改步骤

### 第一步: 修改routes.py，添加手写签名相关功能

在routes.py文件开头添加以下导入语句：

```python
import re
import io
import base64
from io import BytesIO
try:
    from PIL import Image
except ImportError:
    current_app.logger.warning("PIL库未安装，签名功能可能受限")
    Image = None

try:
    import img2pdf
except ImportError:
    current_app.logger.warning("img2pdf库未安装，签名功能可能受限")
    img2pdf = None
```

然后添加以下两个路由函数：

```python
# 在PDF上直接手写签名
@main.route('/file/pdf_signature/<int:file_id>')
@login_required
def pdf_handwriting_signature(file_id):
    try:
        file = File.query.get_or_404(file_id)
        
        # 检查权限
        if not is_super_admin(current_user):
            if not file.folder or not file.folder.can_access(current_user):
                flash('您没有权限为此文件添加签名')
                return redirect(url_for('main.index'))
                
        # 检查文件类型是否是PDF
        if file.file_type != 'application/pdf':
            flash('手写签名功能仅支持PDF文件')
            return redirect(url_for('main.preview_file', file_id=file_id))
        
        # 获取用户已保存的签名
        saved_signatures = []
        try:
            # 获取用户的签名数据
            user_signatures = db.session.query(Signature, UserSignature)\
                .join(UserSignature, UserSignature.signature_id == Signature.id)\
                .filter(UserSignature.user_id == current_user.id)\
                .filter(Signature.signature_type == 'handwriting')\
                .order_by(Signature.signature_date.desc())\
                .limit(5)\
                .all()
                
            for signature, _ in user_signatures:
                saved_signatures.append({
                    'id': signature.id,
                    'date': signature.signature_date.strftime('%Y-%m-%d %H:%M'),
                    'data': signature.signature_data if signature.signature_data else ''
                })
                
        except Exception as e:
            current_app.logger.error(f"获取用户已保存签名时出错: {str(e)}")
        
        # 构建相对路径用于模板
        static_folder = current_app.static_folder
        upload_folder = current_app.config['UPLOAD_FOLDER']
        
        if file.physical_path:
            file_path = file.physical_path
        else:
            file_path = os.path.join(upload_folder, file.filename)
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            flash('文件不存在')
            return redirect(url_for('main.index'))
            
        # 获取相对路径
        if file_path.startswith(static_folder):
            # 如果文件在static目录下
            relative_path = file_path[len(static_folder):].replace('\\', '/')
            if not relative_path.startswith('/'):
                relative_path = '/' + relative_path
            file_url = url_for('static', filename=relative_path.lstrip('/'))
        else:
            # 如果不在static目录下，使用stream路由提供文件
            file_url = url_for('main.stream_file', file_id=file_id)
        
        return render_template('preview/pdf_sign.html', 
                              file_id=file_id, 
                              filename=file.filename, 
                              file_url=file_url,
                              saved_signatures=saved_signatures)
    
    except Exception as e:
        current_app.logger.error(f"访问PDF手写签名页面失败: {str(e)}")
        flash(f"访问PDF手写签名页面失败: {str(e)}")
        return redirect(url_for('main.index'))

# 保存PDF上的手写签名
@main.route('/file/save_pdf_signature/<int:file_id>', methods=['POST'])
@login_required
def save_pdf_signature(file_id):
    try:
        current_app.logger.info(f"接收到保存PDF手写签名请求，文件ID：{file_id}")
        # 获取文件信息
        file = File.query.get_or_404(file_id)
        
        # 检查权限
        if not is_super_admin(current_user):
            if not file.folder or not file.folder.can_access(current_user):
                current_app.logger.warning(f"用户 {current_user.username} 尝试为无权限的文件 {file_id} 添加签名")
                return jsonify({'success': False, 'message': '您没有权限为此文件添加签名'})
        
        # 获取POST数据
        if not request.is_json:
            current_app.logger.error("请求内容不是JSON格式")
            return jsonify({'success': False, 'message': '请求格式错误'})
            
        data = request.get_json()
        screenshot = data.get('screenshot')
        signatures = data.get('signatures', [])
        
        if not screenshot:
            current_app.logger.error("未接收到签名截图数据")
            return jsonify({'success': False, 'message': '未接收到签名截图数据'})
        
        current_app.logger.info(f"接收到 {len(signatures)} 个签名位置数据")
            
        # 创建签名版本文件名
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        filename = f"{file.filename.rsplit('.', 1)[0]}_signed_{timestamp}.pdf"
        signed_path = os.path.join(current_app.config['UPLOAD_FOLDER'], filename)
        
        # 将BASE64数据转换为图像
        try:
            # 去除BASE64头部
            image_data = re.sub('^data:image/.+;base64,', '', screenshot)
            # 将Base64转换为二进制
            image_binary = base64.b64decode(image_data)
            
            # 保存为PDF
            with open(signed_path, 'wb') as f:
                f.write(image_binary)
                
            # 使用img2pdf库将PNG转换为PDF
            try:
                import img2pdf
                
                # 尝试将图像转换为PDF格式
                with open(signed_path, 'wb') as f_pdf:
                    image = Image.open(BytesIO(image_binary))
                    # 确保图像是RGB格式
                    if image.mode != 'RGB':
                        image = image.convert('RGB')
                    pdf_bytes = img2pdf.convert(image)
                    f_pdf.write(pdf_bytes)
            except ImportError:
                current_app.logger.warning("未找到img2pdf库，将直接保存为图像文件")
            except Exception as e:
                current_app.logger.error(f"转换为PDF过程中出错: {str(e)}")
                # 如果转换失败，我们仍然保留原始图像
                
            # 创建新的文件记录
            new_file = File(
                filename=filename,
                file_type='application/pdf',
                user_id=current_user.id,
                folder_id=file.folder_id,
                original_file_id=file.id,
                description=f"{file.filename} - 手写签名版本"
            )
            db.session.add(new_file)
            db.session.flush()  # 获取新文件ID
            
            # 记录手写签名的每个位置
            for idx, sig in enumerate(signatures):
                try:
                    # 创建手写签名记录
                    signature = Signature(
                        file_id=new_file.id,
                        signature_date=datetime.now(),
                        signature_type='handwriting',
                        signature_position=f"x:{sig['x']},y:{sig['y']}"
                    )
                    db.session.add(signature)
                    
                    # 记录用户签名关系
                    user_signature = UserSignature(
                        user_id=current_user.id,
                        signature_id=signature.id
                    )
                    db.session.add(user_signature)
                except Exception as e:
                    current_app.logger.error(f"保存签名位置数据时出错: {str(e)}")
            
            # 提交事务
            db.session.commit()
            current_app.logger.info(f"手写签名成功保存，新文件ID: {new_file.id}")
            
            return jsonify({
                'success': True, 
                'message': '签名已保存',
                'new_file_id': new_file.id
            })
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"保存签名图像时出错: {str(e)}")
            return jsonify({'success': False, 'message': f'保存签名时出错: {str(e)}'})
    
    except Exception as e:
        current_app.logger.error(f"保存PDF签名过程中发生错误: {str(e)}")
        return jsonify({'success': False, 'message': f'处理请求时出错: {str(e)}'})
```

### 第二步：创建PDF手写签名页面模板

创建文件 `app/templates/preview/pdf_sign.html` 内容如下：

```html
{% extends "base.html" %}
{% block content %}
<div class="container-fluid p-0">
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ filename }} - 添加手写签名</h5>
                    <div class="btn-group">
                        <button id="btnSaveSignature" class="btn btn-sm btn-success">
                            <i class="fas fa-save"></i> 保存签名
                        </button>
                        <button id="btnClearSignature" class="btn btn-sm btn-warning">
                            <i class="fas fa-eraser"></i> 清除签名
                        </button>
                        <a href="{{ url_for('main.preview_file', file_id=file_id) }}" class="btn btn-sm btn-secondary">
                            <i class="fas fa-arrow-left"></i> 返回
                        </a>
                    </div>
                </div>
                <div class="card-body p-0 position-relative">
                    <!-- 签名工具栏 -->
                    <div class="bg-light p-2 border-bottom">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                <div class="input-group input-group-sm">
                                    <span class="input-group-text">笔迹颜色</span>
                                    <input type="color" id="penColor" class="form-control form-control-color" value="#000000">
                                </div>
                            </div>
                            <div class="col-auto">
                                <div class="input-group input-group-sm">
                                    <span class="input-group-text">笔迹粗细</span>
                                    <input type="range" id="penSize" class="form-range" min="1" max="10" value="2" style="width: 100px;">
                                    <span class="input-group-text" id="penSizeValue">2px</span>
                                </div>
                            </div>
                            <div class="col-auto">
                                <div class="input-group input-group-sm">
                                    <span class="input-group-text">使用保存的签名</span>
                                    <select id="selectSignature" class="form-select form-select-sm">
                                        <option value="">-- 选择签名 --</option>
                                        {% for signature in saved_signatures %}
                                        <option value="{{ signature.id }}" data-signature="{{ signature.data }}">{{ signature.date }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-auto">
                                <span class="text-muted small">点击PDF文档位置添加签名</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- PDF容器 -->
                    <div id="pdfContainer" class="pdf-container position-relative" style="min-height: 800px;">
                        <!-- iframe用于显示PDF文件 -->
                        <iframe id="pdfViewer" src="{{ file_url }}" style="width: 100%; height: 100%; position: absolute; top: 0; left: 0; border: none; z-index: 1;"></iframe>
                        
                        <!-- 签名层 -->
                        <div id="signatureLayer" class="position-absolute top-0 start-0 w-100 h-100" style="pointer-events: none; z-index: 2;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 签名模态框 -->
<div class="modal fade" id="signatureModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加签名</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3 text-center">
                    <p>请在下方框内签署您的签名</p>
                    <div class="signature-pad-container border rounded" style="background-color: white;">
                        <canvas id="signaturePad" width="450" height="200" style="width: 100%; height: 200px;"></canvas>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="clearPad">清除</button>
                <button type="button" class="btn btn-primary" id="confirmSignature">确认签名</button>
            </div>
        </div>
    </div>
</div>

<style>
    .pdf-container {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        overflow: auto;
        height: calc(100vh - 200px);
    }
    
    .signature-item {
        position: absolute;
        cursor: move;
        z-index: 100;
        pointer-events: auto !important;
    }
    
    .signature-image {
        max-width: 200px;
        border: 1px dashed transparent;
    }
    
    .signature-item:hover .signature-image {
        border-color: #007bff;
    }
    
    .signature-controls {
        display: none;
        z-index: 101;
    }
    
    .signature-item:hover .signature-controls {
        display: block;
    }
    
    /* 确保签名层位于iframe之上 */
    #signatureLayer {
        z-index: 50;
        pointer-events: auto !important;
    }
    
    /* 确保iframe不会拦截鼠标事件 */
    #pdfViewer {
        pointer-events: none;
    }
    
    /* 确保签名模态框显示在最上层 */
    .modal {
        z-index: 9999;
    }
    
    /* 修复移动设备上的触摸事件 */
    @media (hover: none) and (pointer: coarse) {
        #signatureLayer {
            touch-action: none;
        }
        
        .signature-item {
            touch-action: none;
        }
    }
</style>

<script src="https://cdn.jsdelivr.net/npm/signature_pad@4.0.0/dist/signature_pad.umd.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/html2canvas@1.4.1/dist/html2canvas.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log("PDF签名页面初始化...");
    
    // 初始化变量
    const signatureLayer = document.getElementById('signatureLayer');
    const pdfContainer = document.getElementById('pdfContainer');
    const btnSaveSignature = document.getElementById('btnSaveSignature');
    const btnClearSignature = document.getElementById('btnClearSignature');
    const penColor = document.getElementById('penColor');
    const penSize = document.getElementById('penSize');
    const penSizeValue = document.getElementById('penSizeValue');
    const selectSignature = document.getElementById('selectSignature');
    
    // 确保模态框元素存在
    if (!document.getElementById('signatureModal')) {
        console.error("找不到签名模态框元素！");
        alert("页面加载不完整，请刷新页面重试");
        return;
    }
    
    const signatureModal = new bootstrap.Modal(document.getElementById('signatureModal'));
    
    let signatureItems = [];
    let currentSignature = null;
    let isDragging = false;
    let offsetX, offsetY;
    
    // 初始化签名板
    const canvas = document.getElementById('signaturePad');
    if (!canvas) {
        console.error("找不到签名板元素！");
        alert("页面加载不完整，请刷新页面重试");
        return;
    }
    
    const signaturePad = new SignaturePad(canvas, {
        backgroundColor: 'rgb(255, 255, 255)',
        penColor: 'rgb(0, 0, 0)',
        minWidth: 1,
        maxWidth: 4
    });
    
    console.log("签名板初始化完成");
    
    // 自适应调整画布大小
    function resizeCanvas() {
        const ratio = Math.max(window.devicePixelRatio || 1, 1);
        canvas.width = canvas.offsetWidth * ratio;
        canvas.height = canvas.offsetHeight * ratio;
        canvas.getContext("2d").scale(ratio, ratio);
        signaturePad.clear(); // 调整大小后清除
    }
    
    window.addEventListener("resize", resizeCanvas);
    resizeCanvas();
    
    // 修复：将事件监听器添加到signatureLayer而不是pdfContainer
    // 并确保signatureLayer的pointer-events设置为auto
    signatureLayer.style.pointerEvents = 'auto';
    
    signatureLayer.addEventListener('click', function(e) {
        console.log("签名层被点击", e);
        
        // 忽略已有签名上的点击
        if (e.target.closest('.signature-item')) {
            console.log("点击了现有签名，忽略");
            return;
        }
        
        // 计算点击位置
        const rect = pdfContainer.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        console.log(`点击位置: x=${x}, y=${y}`);
        
        // 暂存位置信息
        currentSignature = { x, y };
        
        // 显示签名板
        signatureModal.show();
    });
    
    // 清除签名板
    const clearPadBtn = document.getElementById('clearPad');
    if (clearPadBtn) {
        clearPadBtn.addEventListener('click', function() {
            console.log("清除签名板");
            signaturePad.clear();
        });
    }
    
    // 确认签名按钮点击事件
    const confirmBtn = document.getElementById('confirmSignature');
    if (confirmBtn) {
        confirmBtn.addEventListener('click', function() {
            console.log("确认签名按钮被点击");
            if (signaturePad.isEmpty()) {
                alert('请先签名');
                return;
            }
            
            const signatureData = signaturePad.toDataURL();
            console.log("获取签名数据成功，长度:", signatureData.length);
            addSignatureToDocument(signatureData, currentSignature.x, currentSignature.y);
            signatureModal.hide();
        });
    }
    
    // 如果有预保存的签名，添加选择事件
    if (selectSignature) {
        selectSignature.addEventListener('change', function() {
            console.log("选择了保存的签名");
            const selectedOption = this.options[this.selectedIndex];
            if (!selectedOption.value) return;
            
            // 获取选中的签名数据
            const signatureData = selectedOption.getAttribute('data-signature');
            
            // 询问用户放置位置
            alert('请点击PDF上您希望放置签名的位置');
            
            // 监听一次性点击事件
            const clickHandler = function(e) {
                // 阻止事件冒泡
                e.preventDefault();
                e.stopPropagation();
                
                const rect = pdfContainer.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                console.log(`选择放置位置: x=${x}, y=${y}`);
                addSignatureToDocument(signatureData, x, y);
                
                // 移除事件监听器
                signatureLayer.removeEventListener('click', clickHandler);
                
                // 重置选择框
                selectSignature.selectedIndex = 0;
            };
            
            // 添加一次性点击事件
            signatureLayer.addEventListener('click', clickHandler);
        });
    }
    
    // 添加签名到文档
    function addSignatureToDocument(signatureData, posX, posY) {
        console.log(`添加签名到文档: x=${posX}, y=${posY}`);
        // 创建签名元素
        const signatureItem = document.createElement('div');
        signatureItem.className = 'signature-item';
        signatureItem.style.left = posX + 'px';
        signatureItem.style.top = posY + 'px';
        signatureItem.style.position = 'absolute';
        signatureItem.style.pointerEvents = 'auto'; // 修复：确保签名元素可交互
        signatureItem.style.zIndex = '100'; // 修复：确保签名在最上层
        
        // 创建签名图像
        const signatureImg = document.createElement('img');
        signatureImg.src = signatureData;
        signatureImg.className = 'signature-image';
        signatureImg.alt = '签名';
        signatureImg.draggable = false;
        signatureImg.style.maxHeight = '100px';
        signatureItem.appendChild(signatureImg);
        
        // 创建控制按钮
        const controls = document.createElement('div');
        controls.className = 'signature-controls';
        controls.style.position = 'absolute';
        controls.style.top = '-20px';
        controls.style.right = '0';
        
        const deleteBtn = document.createElement('button');
        deleteBtn.className = 'btn btn-sm btn-danger';
        deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
        deleteBtn.addEventListener('click', function(e) {
            // 阻止事件冒泡
            e.preventDefault();
            e.stopPropagation();
            
            console.log("删除签名");
            signatureLayer.removeChild(signatureItem);
            // 从数组中移除
            const index = signatureItems.findIndex(item => item.element === signatureItem);
            if (index !== -1) {
                signatureItems.splice(index, 1);
            }
        });
        controls.appendChild(deleteBtn);
        signatureItem.appendChild(controls);
        
        // 添加拖拽功能
        signatureItem.addEventListener('mousedown', function(e) {
            // 忽略控制按钮上的点击
            if (e.target.closest('.signature-controls')) {
                return;
            }
            
            // 阻止事件冒泡
            e.preventDefault();
            e.stopPropagation();
            
            console.log("开始拖动签名");
            isDragging = true;
            currentSignature = signatureItem;
            
            const rect = signatureItem.getBoundingClientRect();
            offsetX = e.clientX - rect.left;
            offsetY = e.clientY - rect.top;
            
            // 确保签名元素在拖动时显示在最前面
            signatureItem.style.zIndex = 1000;
        });
        
        // 添加到签名层
        signatureLayer.appendChild(signatureItem);
        signatureItems.push({
            element: signatureItem,
            data: signatureData,
            x: posX,
            y: posY
        });
        
        console.log("签名元素已添加到文档");
    }
    
    // 处理拖拽
    document.addEventListener('mousemove', function(e) {
        if (!isDragging || !currentSignature) return;
        
        const rect = pdfContainer.getBoundingClientRect();
        const x = e.clientX - rect.left - offsetX;
        const y = e.clientY - rect.top - offsetY;
        
        // 限制在PDF容器内
        const maxX = rect.width - currentSignature.offsetWidth;
        const maxY = rect.height - currentSignature.offsetHeight;
        const boundedX = Math.max(0, Math.min(x, maxX));
        const boundedY = Math.max(0, Math.min(y, maxY));
        
        currentSignature.style.left = boundedX + 'px';
        currentSignature.style.top = boundedY + 'px';
        
        // 更新数组中的位置
        const index = signatureItems.findIndex(item => item.element === currentSignature);
        if (index !== -1) {
            signatureItems[index].x = boundedX;
            signatureItems[index].y = boundedY;
        }
    });
    
    document.addEventListener('mouseup', function() {
        if (isDragging) {
            console.log("结束拖动签名");
            // 恢复正常的z-index
            if (currentSignature) {
                currentSignature.style.zIndex = '100';
            }
        }
        isDragging = false;
        currentSignature = null;
    });
    
    // 笔迹粗细变化
    if (penSize && penSizeValue) {
        penSize.addEventListener('input', function() {
            const size = this.value;
            penSizeValue.textContent = size + 'px';
            signaturePad.minWidth = size / 2;
            signaturePad.maxWidth = size * 2;
        });
    }
    
    // 笔迹颜色变化
    if (penColor) {
        penColor.addEventListener('input', function() {
            signaturePad.penColor = this.value;
        });
    }
    
    // 清除所有签名
    if (btnClearSignature) {
        btnClearSignature.addEventListener('click', function() {
            console.log("清除所有签名");
            if (confirm('确定要清除所有签名吗？')) {
                while (signatureLayer.firstChild) {
                    signatureLayer.removeChild(signatureLayer.firstChild);
                }
                signatureItems = [];
            }
        });
    }
    
    // 保存签名
    if (btnSaveSignature) {
        btnSaveSignature.addEventListener('click', function() {
            console.log("保存签名按钮被点击");
            if (signatureItems.length === 0) {
                alert('请先添加至少一个签名');
                return;
            }
            
            // 显示加载提示
            const loadingToast = document.createElement('div');
            loadingToast.className = 'position-fixed top-50 start-50 translate-middle p-3 bg-dark text-white rounded';
            loadingToast.style.zIndex = 9999;
            loadingToast.innerHTML = '<div class="spinner-border spinner-border-sm me-2" role="status"></div> 正在保存签名...';
            document.body.appendChild(loadingToast);
            
            // 使用html2canvas捕获整个PDF和签名
            setTimeout(() => {
                console.log("开始捕获PDF和签名");
                html2canvas(pdfContainer, {
                    allowTaint: true,
                    useCORS: true,
                    logging: true, // 启用日志
                    onclone: function(clonedDoc) {
                        console.log("文档已克隆");
                        // 可以在这里修改克隆的文档
                    }
                }).then(canvas => {
                    console.log("PDF和签名捕获成功");
                    const screenshot = canvas.toDataURL('image/png');
                    console.log("截图数据长度:", screenshot.length);
                    
                    // 准备签名数据
                    const signatures = signatureItems.map(item => ({
                        data: item.data,
                        x: item.x,
                        y: item.y
                    }));
                    
                    console.log("准备发送签名数据到服务器");
                    // 发送到服务器
                    fetch('{{ url_for("main.save_pdf_signature", file_id=file_id) }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: JSON.stringify({
                            screenshot: screenshot,
                            signatures: signatures
                        })
                    })
                    .then(response => {
                        console.log("服务器响应状态:", response.status);
                        return response.json();
                    })
                    .then(data => {
                        document.body.removeChild(loadingToast);
                        console.log("服务器响应数据:", data);
                        if (data.success) {
                            alert('签名保存成功!');
                            window.location.href = '{{ url_for("main.preview_file", file_id=file_id) }}';
                        } else {
                            alert('保存失败: ' + data.message);
                        }
                    })
                    .catch(error => {
                        document.body.removeChild(loadingToast);
                        console.error('Error:', error);
                        alert('保存签名时出错: ' + error.message);
                    });
                }).catch(error => {
                    document.body.removeChild(loadingToast);
                    console.error('截图错误:', error);
                    alert('创建签名截图时出错: ' + error.message);
                });
            }, 500); // 增加延迟，确保UI渲染完成
        });
    }

    // 确保iframe内容加载完成后再绑定事件
    const pdfViewer = document.getElementById('pdfViewer');
    if (pdfViewer) {
        pdfViewer.onload = function() {
            console.log("PDF iframe已加载完成");
            // 确保签名层可交互
            signatureLayer.style.pointerEvents = 'auto';
            // 移除iframe的pointer-events，这样点击事件可以传递到签名层
            pdfViewer.style.pointerEvents = 'none';
            
            console.log("调试信息 - 签名层样式:", {
                pointerEvents: signatureLayer.style.pointerEvents,
                zIndex: signatureLayer.style.zIndex,
                width: signatureLayer.offsetWidth,
                height: signatureLayer.offsetHeight
            });
            
            // 添加测试点击事件
            setTimeout(() => {
                console.log("添加测试点击事件监听...");
                signatureLayer.addEventListener('click', function(e) {
                    console.log("测试点击事件触发 - 位置:", e.clientX, e.clientY);
                }, { once: true });
            }, 2000);
        };
    }

    // 添加触摸设备支持
    function addTouchEvents() {
        signatureLayer.addEventListener('touchstart', function(e) {
            console.log("触摸开始事件触发");
            // 如果用户正在拖动签名，则不触发点击事件
            if (isDragging) return;
            
            // 获取第一个触摸点
            const touch = e.touches[0];
            
            // 忽略已有签名上的触摸
            if (touch.target.closest('.signature-item')) {
                console.log("触摸了现有签名，忽略");
                return;
            }
            
            // 计算触摸位置
            const rect = pdfContainer.getBoundingClientRect();
            const x = touch.clientX - rect.left;
            const y = touch.clientY - rect.top;
            
            console.log(`触摸位置: x=${x}, y=${y}`);
            
            // 暂存位置信息
            currentSignature = { x, y };
            
            // 显示签名板
            signatureModal.show();
            
            // 阻止默认行为和冒泡
            e.preventDefault();
        });
        
        // 给签名项添加触摸拖拽支持
        signatureLayer.addEventListener('touchmove', function(e) {
            if (!isDragging || !currentSignature) return;
            
            // 阻止页面滚动
            e.preventDefault();
            
            const touch = e.touches[0];
            const rect = pdfContainer.getBoundingClientRect();
            const x = touch.clientX - rect.left - offsetX;
            const y = touch.clientY - rect.top - offsetY;
            
            // 限制在PDF容器内
            const maxX = rect.width - currentSignature.offsetWidth;
            const maxY = rect.height - currentSignature.offsetHeight;
            const boundedX = Math.max(0, Math.min(x, maxX));
            const boundedY = Math.max(0, Math.min(y, maxY));
            
            currentSignature.style.left = boundedX + 'px';
            currentSignature.style.top = boundedY + 'px';
            
            // 更新数组中的位置
            const index = signatureItems.findIndex(item => item.element === currentSignature);
            if (index !== -1) {
                signatureItems[index].x = boundedX;
                signatureItems[index].y = boundedY;
            }
        });
        
        signatureLayer.addEventListener('touchend', function() {
            if (isDragging) {
                console.log("触摸拖拽结束");
                if (currentSignature) {
                    currentSignature.style.zIndex = '100';
                }
            }
            isDragging = false;
            currentSignature = null;
        });
    }
    
    // 添加触摸事件支持
    if ('ontouchstart' in window) {
        console.log("检测到触摸设备，添加触摸事件支持");
        addTouchEvents();
    }

    console.log("PDF签名页面初始化完成");
});
</script>
{% endblock %}

### 第三步：更新PDF预览页面，添加手写签名选项

修改 `app/templates/preview/pdf.html` 中的签名按钮下拉菜单，添加手写签名选项链接：

```html
<div class="dropdown">
    <button class="btn btn-sm btn-success dropdown-toggle" type="button" id="signatureDropdown" data-bs-toggle="dropdown" aria-expanded="false">
        <i class="fas fa-signature"></i> 签名
    </button>
    <ul class="dropdown-menu" aria-labelledby="signatureDropdown">
        <li><a class="dropdown-item" href="{{ url_for('main.file_signature', file_id=file_id) }}"><i class="fas fa-certificate"></i> 电子签名</a></li>
        <li><a class="dropdown-item" href="{{ url_for('main.pdf_handwriting_signature', file_id=file_id) }}"><i class="fas fa-pen"></i> 手写签名</a></li>
    </ul>
</div>
```

### 第四步：下载所需的JavaScript库

确保在Ubuntu服务器上下载必要的JavaScript库：

```bash
# 创建必要的目录
mkdir -p static/js/libs

# 下载html2canvas库
curl -o static/js/libs/html2canvas.min.js https://unpkg.com/html2canvas@1.4.1/dist/html2canvas.min.js

# 下载signature_pad库
curl -o static/js/libs/signature_pad.umd.min.js https://cdn.jsdelivr.net/npm/signature_pad@4.0.0/dist/signature_pad.umd.min.js
```

## 3. 调试与解决方法

如果遇到问题，可以使用以下命令查看应用日志：

```bash
tail -f /var/log/paperless/app.log
```

常见问题解决方法：

1. **JavaScript库加载失败**: 先尝试使用CDN链接（如我们在模板中使用的），如果仍有问题，考虑本地下载库文件并更改引用路径。

2. **`iframe`访问权限问题**: 如果PDF无法在iframe中加载或签名功能无法与PDF交互，可能是浏览器安全策略限制，确保PDF文件与应用程序同源。

3. **签名位置偏移**: 可能是因为PDF缩放或窗口大小变化导致，尝试在点击事件中获取准确的偏移量。

4. **导入库错误**: 如果遇到`img2pdf`或`PIL`库导入错误，请使用上述命令安装这些依赖。

5. **PDF保存失败**: 检查服务器上的文件权限，确保应用有权在目标目录中写入文件。

## 4. 手写签名功能常见问题修复

如果手写签名功能不能正常工作，例如无法在PDF上绘制或放置签名，可能是由以下原因造成的：

### 4.1 点击事件无法穿透iframe问题

修复方法：

1. 确保iframe的pointer-events样式设置为none：
```javascript
document.getElementById('pdfViewer').style.pointerEvents = 'none';
```

2. 确保签名层(signatureLayer)位于PDF iframe之上，且可接收点击事件：
```html
<iframe id="pdfViewer" style="z-index: 1;"></iframe>
<div id="signatureLayer" style="z-index: 2; pointer-events: auto;"></div>
```

### 4.2 签名无法移动或删除问题

修复方法：

1. 确保签名元素的pointer-events属性设置为auto：
```javascript
signatureItem.style.pointerEvents = 'auto';
```

2. 确保正确阻止事件冒泡：
```javascript
element.addEventListener('mousedown', function(e) {
    e.preventDefault();
    e.stopPropagation();
    // 其他代码
});
```

### 4.3 移动设备上签名功能不工作

修复方法：

1. 添加触摸事件支持：
```javascript
// 检测设备类型并添加适当的事件监听
if ('ontouchstart' in window) {
    signatureLayer.addEventListener('touchstart', handleTouchStart);
    signatureLayer.addEventListener('touchmove', handleTouchMove);
    signatureLayer.addEventListener('touchend', handleTouchEnd);
}
```

2. 设置适当的CSS以防止触摸设备上的默认行为：
```css
.signature-item, #signatureLayer {
    touch-action: none;
}
```

### 4.4 调试技巧

如果签名功能仍然存在问题，可以添加以下调试代码查看元素的实际状态：

```javascript
console.log("签名层状态:", {
    pointerEvents: signatureLayer.style.pointerEvents,
    zIndex: signatureLayer.style.zIndex,
    width: signatureLayer.offsetWidth,
    height: signatureLayer.offsetHeight
});

// 添加测试点击事件
signatureLayer.addEventListener('click', function(e) {
    console.log("签名层被点击 - 位置:", e.clientX, e.clientY);
});
```

查看浏览器控制台输出，确认事件是否正确触发，元素是否正确定位。 