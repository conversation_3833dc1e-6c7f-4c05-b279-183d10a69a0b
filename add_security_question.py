#!/usr/bin/env python3
from app import create_app, db
from app.models import User
from sqlalchemy import Column, String, text
from sqlalchemy.ext.declarative import declarative_base
import getpass

app = create_app()

def add_security_columns():
    # 运行迁移来添加新列
    with app.app_context():
        # 检查列是否已存在
        inspector = db.inspect(db.engine)
        columns = [col['name'] for col in inspector.get_columns('user')]
        
        # 如果列不存在，添加新列
        if 'security_question' not in columns:
            print("添加安全问题列...")
            with db.engine.connect() as conn:
                conn.execute(text('ALTER TABLE user ADD COLUMN security_question VARCHAR(255)'))
                conn.commit()
            print("安全问题列已添加")
        else:
            print("安全问题列已存在")
            
        if 'security_answer_hash' not in columns:
            print("添加安全问题答案列...")
            with db.engine.connect() as conn:
                conn.execute(text('ALTER TABLE user ADD COLUMN security_answer_hash VARCHAR(128)'))
                conn.commit()
            print("安全问题答案列已添加")
        else:
            print("安全问题答案列已存在")

def set_security_question_for_admin():
    with app.app_context():
        admin = User.query.filter_by(username='cv24051').first()
        if not admin:
            print("错误: 未找到cv24051用户")
            return False
        
        if admin.security_question and admin.security_answer_hash:
            overwrite = input("cv24051用户已有安全问题。是否覆盖? (y/n): ")
            if overwrite.lower() != 'y':
                print("操作已取消")
                return False
        
        print("\n为cv24051用户设置安全问题")
        print("---------------------------")
        
        # 预设问题列表
        preset_questions = [
            "大哥的名字是?",
            "您的第一所学校的名称是什么?",
            "您的第一个宠物的名字是什么?",
            "开发者是?",
            "您最喜欢的老师的名字是什么?"
        ]
        
        # 显示预设问题
        print("\n预设安全问题:")
        for i, question in enumerate(preset_questions, 1):
            print(f"{i}. {question}")
        
        # 允许自定义问题
        print(f"{len(preset_questions) + 1}. 自定义问题")
        
        # 选择问题
        while True:
            try:
                choice = int(input("\n请选择问题 (输入数字): "))
                if 1 <= choice <= len(preset_questions):
                    question = preset_questions[choice - 1]
                    break
                elif choice == len(preset_questions) + 1:
                    question = input("请输入您的自定义问题: ")
                    if question.strip():
                        break
                    print("问题不能为空，请重新输入")
                else:
                    print(f"请输入1到{len(preset_questions) + 1}之间的数字")
            except ValueError:
                print("请输入有效的数字")
        
        # 输入答案
        print("\n请输入安全问题的答案（注意: 答案不区分大小写，但会忽略前后空格）")
        answer = getpass.getpass("答案: ")
        
        if not answer.strip():
            print("错误: 答案不能为空")
            return False
        
        # 确认答案
        confirm_answer = getpass.getpass("确认答案: ")
        
        if answer.strip().lower() != confirm_answer.strip().lower():
            print("错误: 两次输入的答案不匹配")
            return False
        
        # 设置安全问题和答案
        admin.set_security_question(question, answer)
        db.session.commit()
        
        print("\n成功设置安全问题和答案")
        print(f"问题: {question}")
        print("提示: 答案已加密存储，请记住您的答案")
        return True

if __name__ == "__main__":
    print("正在设置安全问题功能...")
    add_security_columns()
    set_security_question_for_admin() 