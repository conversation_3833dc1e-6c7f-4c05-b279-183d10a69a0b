-- 修复MySQL数据库缺失字段
-- 执行前请确保已连接到正确的数据库

USE file_manager;

-- 检查并添加UserSignature表的signature_id字段
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'user_signature'
    AND COLUMN_NAME = 'signature_id'
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE user_signature ADD COLUMN signature_id INT NULL',
    'SELECT "UserSignature表已包含signature_id字段" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加外键约束（如果signature表存在且外键不存在）
SET @table_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.TABLES
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'signature'
);

SET @fk_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'user_signature'
    AND COLUMN_NAME = 'signature_id'
    AND REFERENCED_TABLE_NAME = 'signature'
);

SET @sql = IF(@table_exists > 0 AND @fk_exists = 0 AND @column_exists = 0,
    'ALTER TABLE user_signature ADD CONSTRAINT fk_user_signature_signature FOREIGN KEY (signature_id) REFERENCES signature (id) ON DELETE SET NULL ON UPDATE CASCADE',
    'SELECT "外键约束已存在或不需要添加" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加File表的physical_path字段
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'file'
    AND COLUMN_NAME = 'physical_path'
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE file ADD COLUMN physical_path VARCHAR(500) NULL',
    'SELECT "File表已包含physical_path字段" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 显示修复结果
SELECT 'MySQL数据库字段修复完成！' as result;

-- 验证字段是否添加成功
SELECT 
    'user_signature' as table_name,
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'user_signature'
AND COLUMN_NAME IN ('signature_id', 'user_id', 'signature_data')
ORDER BY ORDINAL_POSITION;

SELECT 
    'file' as table_name,
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'file'
AND COLUMN_NAME IN ('physical_path', 'filename', 'path')
ORDER BY ORDINAL_POSITION;
