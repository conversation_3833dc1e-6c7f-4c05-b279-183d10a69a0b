#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复MySQL数据库缺失字段
- 为UserSignature添加signature_id字段
- 为File添加physical_path字段
"""

import os
import sys
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """主函数，执行MySQL数据库字段修复"""
    try:
        # 导入应用程序
        from app import create_app, db
        from sqlalchemy import inspect, text
        
        app = create_app()
        
        with app.app_context():
            logger.info("开始修复MySQL数据库缺失字段...")
            
            # 获取数据库检查器
            inspector = inspect(db.engine)
            
            # 检查表是否存在
            tables = inspector.get_table_names()
            logger.info(f"数据库中的表: {tables}")
            
            # 修复UserSignature表
            if 'user_signature' in tables:
                logger.info("检查UserSignature表...")
                user_signature_columns = [column['name'] for column in inspector.get_columns('user_signature')]
                logger.info(f"UserSignature表字段: {user_signature_columns}")
                
                if 'signature_id' not in user_signature_columns:
                    logger.info("UserSignature表缺少signature_id字段，正在添加...")
                    try:
                        # MySQL语法添加signature_id列
                        with db.engine.connect() as conn:
                            conn.execute(text("ALTER TABLE user_signature ADD COLUMN signature_id INT NULL"))
                            
                            # 如果signature表存在，添加外键约束
                            if 'signature' in tables:
                                try:
                                    conn.execute(text("""
                                        ALTER TABLE user_signature 
                                        ADD CONSTRAINT fk_user_signature_signature 
                                        FOREIGN KEY (signature_id) REFERENCES signature (id)
                                        ON DELETE SET NULL ON UPDATE CASCADE
                                    """))
                                    logger.info("已添加signature_id外键约束")
                                except Exception as e:
                                    logger.warning(f"添加外键约束失败（可能已存在）: {str(e)}")
                            
                            conn.commit()
                        
                        logger.info("UserSignature表signature_id字段添加成功")
                    except Exception as e:
                        logger.error(f"添加UserSignature.signature_id字段失败: {str(e)}")
                        return False
                else:
                    logger.info("UserSignature表已包含signature_id字段")
            else:
                logger.warning("UserSignature表不存在")
            
            # 修复File表
            if 'file' in tables:
                logger.info("检查File表...")
                file_columns = [column['name'] for column in inspector.get_columns('file')]
                logger.info(f"File表字段: {file_columns}")
                
                if 'physical_path' not in file_columns:
                    logger.info("File表缺少physical_path字段，正在添加...")
                    try:
                        # MySQL语法添加physical_path列
                        with db.engine.connect() as conn:
                            conn.execute(text("ALTER TABLE file ADD COLUMN physical_path VARCHAR(500) NULL"))
                            conn.commit()
                        
                        logger.info("File表physical_path字段添加成功")
                    except Exception as e:
                        logger.error(f"添加File.physical_path字段失败: {str(e)}")
                        return False
                else:
                    logger.info("File表已包含physical_path字段")
            else:
                logger.warning("File表不存在")
            
            logger.info("MySQL数据库字段修复完成！")
            return True
            
    except ImportError as e:
        logger.error(f"导入应用模块失败，请确保您在项目根目录运行此脚本: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"修复过程中发生错误: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ MySQL数据库字段修复成功！请重启应用程序。")
    else:
        print("\n❌ MySQL数据库字段修复失败！请检查错误日志。")
    
    sys.exit(0 if success else 1)
