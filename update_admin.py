from app import create_app, db
from app.models import User
from sqlalchemy.exc import IntegrityError

app = create_app()

def update_admin_username():
    with app.app_context():
        # 查找admin用户
        admin = User.query.filter_by(username='admin').first()
        
        if not admin:
            print("未找到admin用户")
            return
            
        # 检查cv24051用户是否已存在
        existing_user = User.query.filter_by(username='cv24051').first()
        if existing_user:
            print("错误: cv24051用户已存在，请先删除该用户或使用其他名称")
            return
            
        # 修改admin用户名为cv24051
        admin.username = 'cv24051'
        
        try:
            db.session.commit()
            print("成功将admin用户名修改为cv24051")
            print("现在您可以使用新用户名登录系统")
        except IntegrityError:
            db.session.rollback()
            print("更新失败: 数据库完整性错误")
        except Exception as e:
            db.session.rollback()
            print(f"更新失败: {str(e)}")

if __name__ == "__main__":
    update_admin_username() 