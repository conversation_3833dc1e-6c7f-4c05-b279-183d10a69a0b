#!/bin/bash

echo "开始安装手写签名功能所需的依赖..."

# 确保pip已安装
if ! command -v pip3 &> /dev/null; then
    echo "正在安装pip..."
    sudo apt-get update
    sudo apt-get install -y python3-pip
fi

# 安装必要的Python库
echo "安装img2pdf和pillow库..."
pip3 install img2pdf pillow

# 检查安装结果
if python3 -c "import img2pdf; import PIL" 2>/dev/null; then
    echo "依赖安装成功！"
else
    echo "警告: 依赖可能未正确安装，请检查上方错误信息"
    echo "您可以尝试手动安装:"
    echo "  pip3 install img2pdf pillow"
    exit 1
fi

echo ""
echo "现在您可以运行应用程序了:"
echo "  python3 run.py" 