{% extends "base.html" %}
{% block content %}
<div class="container-fluid p-0">
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ filename }}</h5>
                    <div class="btn-group">
                        <a href="{{ url_for('main.download_file', file_id=file_id) }}" class="btn btn-sm btn-primary">
                            <i class="fas fa-download"></i> 下载
                        </a>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-success dropdown-toggle" type="button" id="signatureDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-signature"></i> 签名
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="signatureDropdown">
                                <li><a class="dropdown-item" href="{{ url_for('main.file_signature', file_id=file_id) }}"><i class="fas fa-certificate"></i> 电子签名</a></li>
                                <li><a class="dropdown-item" href="{{ url_for('main.pdf_handwriting_signature', file_id=file_id) }}"><i class="fas fa-pen"></i> 手写签名</a></li>
                            </ul>
                        </div>
                        <a href="{{ url_for('main.index') }}" class="btn btn-sm btn-secondary">
                            <i class="fas fa-arrow-left"></i> 返回
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-12">
            <div class="embed-responsive" style="height: calc(100vh - 150px);">
                <embed 
                    src="{{ file_url }}" 
                    type="application/pdf" 
                    width="100%" 
                    height="100%"
                    class="embed-responsive-item">
            </div>
        </div>
    </div>
</div>
{% endblock %} 