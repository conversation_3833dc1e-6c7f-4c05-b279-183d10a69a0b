from app import create_app, db
from app.models import UserLog, User
from sqlalchemy import text
import time

app = create_app()
with app.app_context():
    try:
        print("开始修复日志数据...")
        
        # 1. 检查日志表是否存在
        with db.engine.connect() as conn:
            result = conn.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='user_log'"))
            if not result.fetchone():
                print("UserLog表不存在，无需修复")
                exit(0)
        
        # 2. 查找并清理重复ID
        print("检查重复ID...")
        
        # 获取所有日志ID
        with db.engine.connect() as conn:
            result = conn.execute(text("SELECT id, COUNT(*) as count FROM user_log GROUP BY id HAVING count > 1"))
            duplicate_ids = [row[0] for row in result.fetchall()]
        
        if duplicate_ids:
            print(f"发现 {len(duplicate_ids)} 个重复ID: {duplicate_ids}")
            
            # 修复重复ID
            for dup_id in duplicate_ids:
                # 获取重复项
                logs = UserLog.query.filter_by(id=dup_id).all()
                print(f"ID {dup_id} 有 {len(logs)} 条重复记录")
                
                # 保留第一条，其他的重新分配ID
                for i, log in enumerate(logs[1:], 1):
                    # 查找最大ID
                    max_id_result = db.session.execute(text("SELECT MAX(id) FROM user_log")).fetchone()
                    max_id = max_id_result[0] if max_id_result and max_id_result[0] else 0
                    
                    # 设置新ID
                    new_id = max_id + i
                    print(f"  将重复ID {dup_id} 更改为新ID {new_id}")
                    
                    with db.engine.connect() as conn:
                        conn.execute(text(f"UPDATE user_log SET id = {new_id} WHERE id = {dup_id} AND rowid != (SELECT MIN(rowid) FROM user_log WHERE id = {dup_id})"))
                
            db.session.commit()
            print("重复ID修复完成")
        else:
            print("未发现重复ID")
        
        # 3. 检查空值并修复
        print("检查日志记录中的空值...")
        logs_with_null = UserLog.query.filter(
            (UserLog.user_id == None) | 
            (UserLog.action == None) | 
            (UserLog.timestamp == None)
        ).all()
        
        if logs_with_null:
            print(f"发现 {len(logs_with_null)} 条有空值的记录")
            for log in logs_with_null:
                print(f"修复ID为 {log.id} 的日志记录")
                # 对于缺少用户ID的记录，使用系统管理员ID
                admin = User.query.filter_by(username='admin').first()
                if not log.user_id and admin:
                    log.user_id = admin.id
                    print(f"  设置用户ID为管理员: {admin.id}")
                
                # 对于缺少操作类型的记录，设置为SYSTEM
                if not log.action:
                    log.action = "SYSTEM"
                    print(f"  设置操作类型为SYSTEM")
                
                # 对于缺少时间戳的记录，设置为当前时间
                if not log.timestamp:
                    from datetime import datetime
                    import pytz
                    log.timestamp = datetime.now(pytz.timezone('Asia/Shanghai'))
                    print(f"  设置时间戳为当前时间")
            
            db.session.commit()
            print("空值修复完成")
        else:
            print("未发现空值记录")
        
        # 4. 检查外键约束
        print("检查用户外键约束...")
        orphan_logs = UserLog.query.filter(~UserLog.user_id.in_(
            db.session.query(User.id)
        )).all()
        
        if orphan_logs:
            print(f"发现 {len(orphan_logs)} 条孤立日志记录（用户已删除）")
            admin = User.query.filter_by(username='admin').first()
            
            if admin:
                for log in orphan_logs:
                    print(f"将ID为 {log.id} 的日志记录关联到管理员用户")
                    log.user_id = admin.id
                
                db.session.commit()
                print("孤立记录修复完成")
            else:
                print("无法修复孤立记录，未找到管理员用户")
        else:
            print("未发现孤立记录")
            
        print("日志修复完成！")
        
    except Exception as e:
        import traceback
        print(f"修复失败: {str(e)}")
        print(traceback.format_exc())
        db.session.rollback() 