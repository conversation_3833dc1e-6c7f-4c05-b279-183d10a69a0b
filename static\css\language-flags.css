/* 语言切换相关样式 */
.language-selector {
    margin-right: 15px;
    display: flex;
    align-items: center;
}

.language-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 4px 8px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    background-color: #fff;
    cursor: pointer;
    font-size: 14px;
    color: #495057;
    text-decoration: none;
    margin-left: 5px;
    transition: all 0.2s ease;
}

.language-button:hover {
    background-color: #f8f9fa;
    border-color: #c6c9cc;
}

.language-button.active {
    border-color: #0d6efd;
    background-color: #e9f0fe;
    color: #0d6efd;
}

.flag-icon {
    display: inline-block;
    width: 20px;
    height: 14px;
    margin-right: 5px;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
}

.flag-icon-zh {
    background-image: url('../images/flag-zh.svg');
}

.flag-icon-en {
    background-image: url('../images/flag-en.svg');
}

.language-dropdown .dropdown-item {
    display: flex;
    align-items: center;
}

.language-dropdown .dropdown-item .flag-icon {
    margin-right: 8px;
}

/* 移动设备适配 */
@media (max-width: 576px) {
    .language-selector {
        margin-right: 5px;
    }
    
    .language-button {
        padding: 2px 5px;
        font-size: 12px;
    }
    
    .flag-icon {
        width: 16px;
        height: 11px;
        margin-right: 3px;
    }
} 