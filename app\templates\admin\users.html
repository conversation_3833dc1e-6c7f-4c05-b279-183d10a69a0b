{% extends "base.html" %}
{% block content %}
<div class="container-fluid p-0">
    <div class="row">
        <div class="col-md-5">
            <div class="card">
                <div class="card-body">
                    <h3 class="card-title">添加新用户</h3>
                    <form method="POST">
                        <div class="mb-3">
                            <label for="username" class="form-label">用户名</label>
                            <input type="text" class="form-control" id="username" name="username" required>
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">密码</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                        <!-- 只有超级管理员才显示设为高级用户的选项 -->
                        {% if current_user.username == 'cv24051' %}
                        <div class="mb-3">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="is_admin" name="is_admin">
                                <label class="form-check-label" for="is_admin">设为高级用户</label>
                            </div>
                        </div>
                        {% endif %}
                        <button type="submit" class="btn btn-primary">添加用户</button>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-7">
            <div class="card">
                <div class="card-body">
                    <h3 class="card-title">用户列表</h3>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>用户名</th>
                                    <th>角色</th>
                                    <th>文件夹权限</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in users %}
                                <tr>
                                    <td>{{ user.username }}</td>
                                    <td>
                                        {% if user.username == 'cv24051' %}
                                        <span class="badge bg-danger">超级管理员</span>
                                        {% elif user.is_admin %}
                                        <span class="badge bg-primary">高级用户</span>
                                        {% else %}
                                        <span class="badge bg-secondary">普通用户</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <!-- 文件夹权限列表 -->
                                        <div class="d-flex flex-wrap gap-1">
                                            {% for folder in user.folders %}
                                            <span class="badge bg-info">
                                                {{ folder.name }}
                                                {% if folder.is_public %}
                                                <i class="fas fa-globe" title="公开"></i>
                                                {% endif %}
                                                {% if folder.read_only %}
                                                <i class="fas fa-lock" title="只读"></i>
                                                {% endif %}
                                            </span>
                                            {% endfor %}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <!-- 只有cv24051才能修改用户密码 -->
                                            {% if current_user.username == 'cv24051' %}
                                            <a href="{{ url_for('main.admin_reset_password', user_id=user.id) }}" 
                                               class="btn btn-sm btn-outline-warning">
                                                修改密码
                                            </a>
                                            {% endif %}
                                            
                                            <!-- cv24051用户不显示权限修改按钮和删除按钮 -->
                                            {% if user.username != 'cv24051' %}
                                                <!-- 不能修改自己的管理员权限 -->
                                                {% if current_user.id != user.id %}
                                                
                                                <!-- 只有超级管理员可以设置高级用户 -->
                                                {% if current_user.username == 'cv24051' %}
                                                <button class="btn btn-sm btn-outline-primary" 
                                                        onclick="toggleAdmin({{ user.id }}, '{{ user.username }}')">
                                                    {% if user.is_admin %}
                                                    取消高级用户
                                                    {% else %}
                                                    设为高级用户
                                                    {% endif %}
                                                </button>
                                                {% endif %}
                                                
                                                <!-- 用户组管理按钮 -->
                                                <button class="btn btn-sm btn-outline-info" 
                                                        onclick="showUserGroups({{ user.id }}, '{{ user.username }}')">
                                                    管理用户组
                                                </button>
                                                
                                                <!-- 删除按钮 - 只有超级管理员可以删除用户 -->
                                                {% if current_user.username == 'cv24051' %}
                                                <button class="btn btn-sm btn-outline-danger" 
                                                        onclick="deleteUser({{ user.id }}, '{{ user.username }}')">
                                                    删除
                                                </button>
                                                {% endif %}
                                                {% endif %}
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 文件夹权限管理模态框 -->
<div class="modal fade" id="userFoldersModal">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">管理用户文件夹权限</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="selectedUserId">
                
                <div class="mb-3">
                    <h6>用户的文件夹</h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>文件夹名</th>
                                    <th>公开访问</th>
                                    <th>只读模式</th>
                                    <th>允许访问的用户</th>
                                </tr>
                            </thead>
                            <tbody id="userFoldersList">
                                <!-- 动态加载文件夹列表 -->
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <div class="mb-3">
                    <h6>可访问的其他文件夹</h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>文件夹名</th>
                                    <th>所有者</th>
                                    <th>权限</th>
                                </tr>
                            </thead>
                            <tbody id="sharedFoldersList">
                                <!-- 动态加载共享文件夹列表 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 用户组管理模态框 -->
<div class="modal fade" id="userGroupsModal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">管理用户组</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="userGroupsUserId">
                
                <div class="mb-3">
                    <h6>用户: <span id="userGroupsUsername"></span></h6>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">选择用户组</label>
                    <select class="form-select" id="userGroupsSelect" multiple size="8">
                        <!-- 用户组列表将通过JavaScript加载 -->
                    </select>
                    <small class="form-text text-muted">按住Ctrl键可选择多个组</small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveUserGroups()">保存</button>
            </div>
        </div>
    </div>
</div>

<script>
function showUserFolders(userId) {
    document.getElementById('selectedUserId').value = userId;
    
    // 获取用户的文件夹权限信息
    fetch(`/admin/user/${userId}/folders`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 更新用户的文件夹列表
                const userFoldersList = document.getElementById('userFoldersList');
                userFoldersList.innerHTML = data.user_folders.map(folder => `
                    <tr>
                        <td>${folder.name}</td>
                        <td>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" 
                                       ${folder.is_public ? 'checked' : ''}
                                       onchange="updateFolderPermission(${folder.id}, 'is_public', this.checked)">
                            </div>
                        </td>
                        <td>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox"
                                       ${folder.read_only ? 'checked' : ''}
                                       onchange="updateFolderPermission(${folder.id}, 'read_only', this.checked)">
                            </div>
                        </td>
                        <td>
                            <select class="form-select form-select-sm" multiple
                                    onchange="updateFolderAllowedUsers(${folder.id}, this)">
                                ${data.all_users.map(user => `
                                    <option value="${user.id}" 
                                            ${folder.allowed_users.includes(user.id) ? 'selected' : ''}>
                                        ${user.username}
                                    </option>
                                `).join('')}
                            </select>
                        </td>
                    </tr>
                `).join('');
                
                // 更新共享文件夹列表
                const sharedFoldersList = document.getElementById('sharedFoldersList');
                sharedFoldersList.innerHTML = data.shared_folders.map(folder => `
                    <tr>
                        <td>${folder.name}</td>
                        <td>${folder.owner_name}</td>
                        <td>
                            ${folder.is_public ? '<span class="badge bg-success">公开</span>' : ''}
                            ${folder.read_only ? '<span class="badge bg-warning">只读</span>' : ''}
                        </td>
                    </tr>
                `).join('');
                
                // 显示模态框
                new bootstrap.Modal(document.getElementById('userFoldersModal')).show();
            } else {
                alert(data.message || '获取权限信息失败');
            }
        })
        .catch(error => alert('操作失败'));
}

function updateFolderPermission(folderId, field, value) {
    fetch(`/folder/${folderId}/permissions`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            [field]: value
        })
    })
    .then(response => response.json())
    .then(data => {
        if (!data.success) {
            alert(data.message || '更新权限失败');
            // 恢复原始状态
            window.location.reload();
        }
    })
    .catch(error => {
        alert('操作失败');
        window.location.reload();
    });
}

function updateFolderAllowedUsers(folderId, select) {
    const allowedUsers = Array.from(select.selectedOptions)
        .map(option => option.value)
        .join(',');
    
    fetch(`/folder/${folderId}/permissions`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            allowed_users: allowedUsers
        })
    })
    .then(response => response.json())
    .then(data => {
        if (!data.success) {
            alert(data.message || '更新权限失败');
            window.location.reload();
        }
    })
    .catch(error => {
        alert('操作失败');
        window.location.reload();
    });
}

function toggleAdmin(userId, username) {
    if(confirm(`确定要${document.querySelector(`button[onclick="toggleAdmin(${userId}, '${username}')"]`).innerText.includes('取消') ? '取消' : '授予'} ${username} 的高级用户权限吗？`)) {
        fetch(`/admin/users/${userId}/toggle_admin`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            }
        })
        .then(response => response.json())
        .then(data => {
            if(data.success) {
                alert(data.message);
                window.location.reload();
            } else {
                alert(data.message || '操作失败');
            }
        })
        .catch(error => alert('操作失败'));
    }
}

function deleteUser(userId, username) {
    if(confirm(`确定要删除用户 ${username} 吗？\n\n此操作将同时删除该用户的所有文件和文件夹，且无法恢复！`)) {
        window.location.href = `/admin/users/delete/${userId}`;
    }
}

// 显示用户组管理模态框
function showUserGroups(userId, username) {
    document.getElementById('userGroupsUserId').value = userId;
    document.getElementById('userGroupsUsername').textContent = username;
    
    // 获取所有用户组和用户当前所在的组
    fetch(`/api/users/${userId}/groups`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const select = document.getElementById('userGroupsSelect');
                select.innerHTML = '';
                
                // 添加所有用户组选项
                data.groups.forEach(group => {
                    const option = document.createElement('option');
                    option.value = group.id;
                    option.textContent = group.name;
                    option.selected = data.user_groups.includes(group.id);
                    select.appendChild(option);
                });
                
                // 显示模态框
                const modal = new bootstrap.Modal(document.getElementById('userGroupsModal'));
                modal.show();
            } else {
                alert(data.message || '无法加载用户组数据');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('加载用户组数据失败');
        });
}

// 保存用户组变更
function saveUserGroups() {
    const userId = document.getElementById('userGroupsUserId').value;
    const select = document.getElementById('userGroupsSelect');
    
    // 获取选中的用户组ID
    const groupIds = Array.from(select.selectedOptions).map(option => parseInt(option.value));
    
    // 发送请求
    fetch(`/api/users/${userId}/groups`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ group_ids: groupIds })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 关闭模态框
            bootstrap.Modal.getInstance(document.getElementById('userGroupsModal')).hide();
            
            // 显示成功消息
            alert(data.message || '用户组已更新');
            
            // 刷新页面
            window.location.reload();
        } else {
            alert(data.message || '更新用户组失败');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('操作失败');
    });
}
</script>
{% endblock %} 