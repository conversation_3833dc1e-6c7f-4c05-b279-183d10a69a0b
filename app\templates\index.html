{% extends "base.html" %}
{% block content %}
<script>
    // Define the currentFolderId variable based on the current folder
    {% if current_folder %}
    var currentFolderId = {{ current_folder.id }};
    {% else %}
    var currentFolderId = null;
    {% endif %}
    
    // Define loadFolder and loadRootFolder functions if they don't exist elsewhere
    function loadFolder(folderId) {
        // Get the base URL by removing any folder path
        const basePath = window.location.pathname.split('/folder/')[0];
        // Make sure we don't create a URL with double slashes
        const baseUrl = basePath === '' ? '/' : basePath;
        window.location.href = `${baseUrl}${baseUrl.endsWith('/') ? '' : '/'}folder/${folderId}`;
    }
    
    function loadRootFolder() {
        // Get the base URL (root of the application)
        const basePath = window.location.pathname.split('/folder/')[0];
        // If we're at the root already, just use '/', otherwise use the base path
        const baseUrl = basePath === '' ? '/' : basePath;
        window.location.href = baseUrl.endsWith('/') ? baseUrl : `${baseUrl}/`;
    }
</script>

<div class="container-fluid p-0">
    <!-- 修改顶部导航栏布局 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="{{ url_for('main.index') }}">
                        <i class="fas fa-home home-icon"></i>
                        {{ t('Home') }}
                    </a>
                </li>
                {% for folder in breadcrumbs %}
                <li class="breadcrumb-item {% if folder == current_folder %}active{% endif %}">
                    {% if folder != current_folder %}
                    <a href="{{ url_for('main.index', folder_id=folder.id) }}">{{ folder.name }}</a>
                    {% else %}
                    {{ folder.name }}
                    {% endif %}
                </li>
                {% endfor %}
            </ol>
        </nav>
    </div>

    <!-- 搜索栏和按钮组 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <!-- 搜索栏 -->
        <div class="search-area d-flex align-items-center">
            <form id="searchForm" class="d-flex gap-2">
                <div class="input-group" style="width: 200px;">
                    <input type="text" class="form-control form-control-sm" id="filename" name="filename" placeholder="{{ t('Filename') }}">
                    <select class="form-select form-select-sm" id="filenameMatch" name="filenameMatch" style="max-width: 70px;">
                        <option value="fuzzy">{{ t('Fuzzy') }}</option>
                        <option value="exact">{{ t('Exact') }}</option>
                    </select>
                </div>
                
                <input type="text" class="form-control form-control-sm" id="tags" name="tags" 
                       placeholder="{{ t('Tags (comma separated)') }}" title="{{ t('Enter multiple tags, separated by commas') }}" 
                       style="width: 120px;">
                <select class="form-select form-select-sm" id="searchScope" name="searchScope" style="width: 120px;">
                    {% if current_folder %}
                    <option value="current">{{ t('Current folder') }}</option>
                    <option value="sub">{{ t('Include subfolders') }}</option>
                    {% else %}
                    <option value="current">{{ t('Root directory') }}</option>
                    {% endif %}
                    <option value="all" {% if not current_folder %}selected{% endif %}>{{ t('All folders') }}</option>
                </select>
                <button type="submit" class="btn btn-primary btn-sm">
                    <i class="fas fa-search"></i> {{ t('Search') }}
                </button>
            </form>
        </div>
        
        <!-- 高级筛选折叠按钮 -->
        <div class="d-flex gap-2">
            <button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#advancedFilter" aria-expanded="false" aria-controls="advancedFilter" style="margin-right: 10px;">
                <i class="fas fa-filter"></i> {{ t('Advanced Filter') }}
            </button>
            <label for="folderUpload" class="btn btn-sm btn-success">
                <i class="fas fa-folder-upload"></i> {{ t('Upload Folder') }}
            </label>
            <input type="file" id="folderUpload" webkitdirectory directory multiple style="display:none" onchange="handleFolderUpload(this.files)">
            <button class="btn btn-sm btn-primary" onclick="showMoveModal()">
                <i class="fas fa-folder-open"></i> {{ t('Move To') }}
            </button>
            <button class="btn btn-sm btn-primary" onclick="showNewFolderModal()">
                <i class="fas fa-folder-plus"></i> {{ t('New Folder') }}
            </button>
        </div>
    </div>

    <!-- 高级筛选折叠面板 -->
    <div class="collapse mb-4" id="advancedFilter" style="margin-left: -10px;">
        <div class="card">
            <div class="card-body">
                <form id="keywordFilterForm" method="get" action="{{ url_for('main.index') }}" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">{{ t('Select Folder') }}</label>
                        <select class="form-select" name="selected_folder_id" id="selectedFolder">
                            <option value="">{{ t('All Folders') }}</option>
                            {% for folder in all_folders %}
                            <option value="{{ folder.id }}" {% if folder.id|string == selected_folder_id %}selected{% endif %}>
                                {{ folder.name }}
                            </option>
                            {% endfor %}
                        </select>
                        <div class="form-text">{{ t('Select folder to filter') }}</div>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">{{ t('Keyword') }} 1</label>
                        <input type="text" class="form-control" name="keyword1" id="keyword1" list="keyword1List" value="{{ keyword1 }}">
                        <datalist id="keyword1List"></datalist>
                        <div class="form-text">{{ t('Enter or select first keyword') }}</div>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">{{ t('Keyword') }} 2</label>
                        <input type="text" class="form-control" name="keyword2" id="keyword2" list="keyword2List" value="{{ keyword2 }}">
                        <datalist id="keyword2List"></datalist>
                        <div class="form-text">{{ t('Enter or select second keyword') }}</div>
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-filter"></i> {{ t('Filter') }}
                        </button>
                        <a href="{{ url_for('main.index') }}" class="btn btn-secondary">
                            <i class="fas fa-sync"></i> {{ t('Reset') }}
                        </a>
                    </div>
                    
                    <!-- 添加隐藏的搜索范围字段 -->
                    <input type="hidden" name="searchScope" value="all">
                </form>
            </div>
        </div>
    </div>

    {% if is_filtering %}
    <!-- 筛选结果提示 -->
    <div class="filtered-results mb-4 p-3">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h5 class="mb-2"><i class="fas fa-filter"></i> {{ t('Filter Results') }}</h5>
                <div class="filter-details">
                    {% if selected_folder_id %}
                        <span class="badge bg-secondary me-2">
                            {{ t('Folder') }}: {{ all_folders|selectattr('id', 'eq', selected_folder_id|int)|first|attr('name') }}
                        </span>
                    {% endif %}
                    
                    {% if keyword1 %}
                        <span class="badge bg-info me-2">{{ t('Keyword') }}: {{ keyword1 }}</span>
                    {% endif %}
                    
                    {% if keyword2 %}
                        <span class="badge bg-info me-2">{{ t('Keyword') }}: {{ keyword2 }}</span>
                    {% endif %}
                    
                    {% if filename %}
                        <span class="badge bg-primary me-2">
                            {{ t('Filename') }} {{ t('contains') if filename_match == 'fuzzy' else t('equals') }}: {{ filename }}
                        </span>
                    {% endif %}
                    
                    {% if tags %}
                        <span class="badge bg-success me-2">
                            {{ t('Tags') }}: {{ tags }}
                        </span>
                    {% endif %}
                    
                    {% if search_scope != 'current' %}
                        <span class="badge bg-warning me-2">
                            {{ t('Search scope') }}: {% if search_scope == 'all' %}
                                {{ t('All folders') }}
                            {% elif search_scope == 'sub' %}
                                {{ t('Including subfolders') }}
                            {% else %}
                                {{ t('Current folder') }}
                            {% endif %}
                        </span>
                    {% endif %}
                </div>
                <div class="mt-2 result-count">
                    <span id="folderCount" class="text-muted"></span>
                    <span id="fileCount" class="text-muted ms-3"></span>
                </div>
            </div>
            <a href="{{ url_for('main.index', folder_id=current_folder.id if current_folder else None) }}" class="btn btn-outline-secondary">
                <i class="fas fa-times"></i> {{ t('Clear Filter') }}
            </a>
        </div>
    </div>
    {% endif %}

    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">{% if current_folder %}{{ current_folder.name }}{% endif %}</h2>
    </div>

    <div class="table-responsive{% if is_filtering %} filtered-table{% endif %}">
        <table class="table table-hover">
            <thead class="table-light">
                <tr>
                    <th style="width: 40px;">
                        <input type="checkbox" class="form-check-input" id="selectAll">
                    </th>
                    <th style="min-width: 400px;">
                        <div class="d-flex align-items-center gap-3">
                            <div class="d-flex align-items-center">
                                <label for="selectAll" class="form-check-label me-2">{{ t('Select All') }}</label>
                                <span id="selectedCount" class="text-muted" style="font-size: 0.9rem;"></span>
                            </div>
                            <div class="d-flex gap-2" id="batchOperations" style="display: none; margin-left: -40px;">
                                <button class="btn btn-sm btn-danger me-1" onclick="deleteSelectedItems()">{{ t('Delete') }}</button>
                                <button class="btn btn-sm btn-secondary me-1" onclick="downloadSelectedFiles()">{{ t('Download') }}</button>
                                <button class="btn btn-sm btn-info" onclick="showTagManageModal()">
                                    <i class="fas fa-tags"></i> {{ t('Manage Tags') }}
                                </button>
                            </div>
                        </div>
                    </th>
                    <th class="text-start">{{ t('Type') }}</th>
                    <th class="text-start">{{ t('Size') }}</th>
                    <th class="text-start">{{ t('Modified Date') }}</th>
                    <th class="text-start">{{ t('Tags') }}</th>
                    <th class="text-start">{{ t('Actions') }}</th>
                </tr>
            </thead>
            <tbody>
                <!-- 文件夹 -->
                {% for folder in folders %}
                <tr>
                    <td class="text-center">
                        <input type="checkbox" class="form-check-input item-checkbox" 
                               data-type="folder" value="{{ folder.id }}">
                    </td>
                    <td>
                        <div class="d-flex align-items-center gap-2">
                            <i class="fas fa-folder text-warning"></i>
                            <a href="{{ url_for('main.index', folder_id=folder.id) }}" class="text-decoration-none">
                                <span class="filename">{{ folder.name }}</span>
                            </a>
                            {% if folder.is_public %}
                            <i class="fas fa-globe text-info" title="{{ t('Public folder') }}"></i>
                            {% endif %}
                            {% if folder.read_only %}
                            <i class="fas fa-lock text-secondary" title="{{ t('Read only') }}"></i>
                            {% endif %}
                            {% if current_user.id|string in folder.allowed_users.split(',') %}
                            <i class="fas fa-user-check text-success" title="{{ t('Authorized access') }}"></i>
                            {% endif %}
                            {% if current_user.is_admin %}
                            <span class="badge bg-primary">{{ t('Admin visible') }}</span>
                            {% endif %}
                            <i class="fas fa-edit text-secondary ms-2 rename-icon" onclick="startRenameFolder(event, this, {{ folder.id }})" style="cursor: pointer; font-size: 0.9em;"></i>
                        </div>
                    </td>
                    <td class="text-start">{{ t('Folder') }}</td>
                    <td class="text-start">-</td>
                    <td class="text-start">{{ folder.create_date|format_datetime }}</td>
                    <td class="text-start">
                        <div class="d-flex align-items-center gap-2 tag-container">
                            <div class="d-flex flex-wrap gap-1 tags-wrapper">
                                {% if folder.tags %}
                                    {% for tag in folder.tags.split(',') if tag %}
                                    <span class="badge bg-info d-flex align-items-center gap-1 tag-item">
                                        <span class="tag-text" onclick="editTag(this, 'folder')">{{ tag }}</span>
                                        <button type="button" class="btn-close btn-close-white btn-sm" 
                                                style="font-size: 0.5rem;" 
                                                onclick="removeTag({{ folder.id }}, '{{ tag }}', 'folder')"></button>
                                    </span>
                                    {% endfor %}
                                {% endif %}
                            </div>
                            <div class="tag-actions">
                                <button class="btn btn-outline-secondary btn-sm" 
                                        onclick="addNewTag(this, {{ folder.id }}, 'folder')"
                                        title="{{ t('Add tag') }}">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                    </td>
                    <td class="text-start">
                        <div class="btn-group btn-group-sm">
                            {% if folder.user_id == current_user.id or current_user.is_admin %}
                            <button class="btn btn-outline-primary" onclick="showFolderPermissions({{ folder.id }})">
                                <i class="fas fa-key"></i> {{ t('Permissions') }}
                            </button>
                            {% endif %}
                            <button class="btn btn-outline-danger" onclick="deleteFolder({{ folder.id }})">{{ t('Delete') }}</button>
                        </div>
                    </td>
                </tr>
                {% endfor %}

                <!-- 文件 -->
                {% for file in files %}
                <tr>
                    <td class="text-center">
                        <input type="checkbox" class="form-check-input item-checkbox" 
                               data-type="file" value="{{ file.id }}">
                    </td>
                    <td>
                        <div class="d-flex align-items-center gap-2">
                            <i class="fas fa-file text-primary"></i>
                            <span class="filename" title="{{ file.original_filename }}" onclick="previewFile({{ file.id }})" style="cursor: pointer; color: #0d6efd;">
                                {{ file.original_filename }}
                            </span>
                            <i class="fas fa-edit text-secondary ms-2 rename-icon" onclick="startRenameFile(event, this, {{ file.id }})" style="cursor: pointer; font-size: 0.9em;"></i>
                        </div>
                    </td>
                    <td class="text-start">{{ file.file_type or '-' }}</td>
                    <td class="text-start">{{ file.file_size|format_size }}</td>
                    <td class="text-start">{{ file.upload_date|format_datetime }}</td>
                    <td class="text-start">
                        <div class="d-flex align-items-center gap-2 tag-container">
                            <div class="d-flex flex-wrap gap-1 tags-wrapper">
                                {% if file.tags %}
                                    {% for tag in file.tags.split(',') if tag %}
                                    <span class="badge bg-info d-flex align-items-center gap-1 tag-item">
                                        <span class="tag-text" onclick="editTag(this, 'file')">{{ tag }}</span>
                                        <button type="button" class="btn-close btn-close-white btn-sm" 
                                                style="font-size: 0.5rem;" 
                                                onclick="removeTag({{ file.id }}, '{{ tag }}', 'file')"></button>
                                    </span>
                                    {% endfor %}
                                {% endif %}
                            </div>
                            <div class="tag-actions">
                                <button class="btn btn-outline-secondary btn-sm" 
                                        onclick="addNewTag(this, {{ file.id }}, 'file')"
                                        title="{{ t('Add tag') }}">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                    </td>
                    <td class="text-start">
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="previewFile({{ file.id }})">
                                <i class="fas fa-eye"></i> {{ t('Preview') }}
                            </button>
                            {% if current_user.is_admin %}
                            <button class="btn btn-outline-secondary" onclick="showMoveFileModal({{ file.id }})">
                                <i class="fas fa-exchange-alt"></i> {{ t('Move') }}
                            </button>
                            {% endif %}
                            <button class="btn btn-outline-danger" onclick="deleteFile({{ file.id }})">{{ t('Delete') }}</button>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<!-- 新建文件夹模态框 -->
<div class="modal fade" id="newFolderModal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ t('New Folder') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">{{ t('Folder Name') }}</label>
                    <input type="text" class="form-control" id="folderName">
                </div>
                
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="isPublic">
                        <label class="form-check-label" for="isPublic">
                            {{ t('Public Access') }}
                        </label>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="readOnly">
                        <label class="form-check-label" for="readOnly">
                            {{ t('Read Only') }}
                        </label>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">{{ t('Allowed Users') }}</label>
                    <select class="form-select" id="allowedUsers" multiple>
                        {% for user in users if not user.is_admin %}
                        <option value="{{ user.id }}">{{ user.username }}</option>
                        {% endfor %}
                    </select>
                    <div class="form-text">{{ t('Hold Ctrl key to select multiple') }}</div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ t('Cancel') }}</button>
                <button type="button" class="btn btn-primary" onclick="createFolder()">{{ t('Create') }}</button>
            </div>
        </div>
    </div>
</div>

<!-- 文件夹权限管理模态框 -->
<div class="modal fade" id="folderPermissionModal" tabindex="-1" aria-labelledby="folderPermissionModalLabel">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="folderPermissionModalLabel">{{ t('Folder Permission Settings') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="permissionFolderId">
                
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="permissionIsPublic">
                        <label class="form-check-label" for="permissionIsPublic">
                            {{ t('Public Access') }}
                        </label>
                        <small class="form-text text-muted d-block">
                            {{ t('Allow all users to access this folder') }}
                        </small>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="permissionReadOnly">
                        <label class="form-check-label" for="permissionReadOnly">
                            {{ t('Read Only') }}
                        </label>
                        <small class="form-text text-muted d-block">
                            {{ t('Other users can only view, not modify folder contents') }}
                        </small>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label" for="permissionAllowedUsers">{{ t('Allowed Users') }}</label>
                    <select class="form-select" id="permissionAllowedUsers" multiple>
                        {% for user in users %}
                        <option value="{{ user.id }}">{{ user.username }}</option>
                        {% endfor %}
                    </select>
                    <small class="form-text text-muted">
                        {{ t('Hold Ctrl key to select multiple. Selected users will have access to this folder') }}
                    </small>
                </div>
                
                <div class="mb-3">
                    <label class="form-label" for="permissionAllowedGroups">{{ t('Allowed User Groups') }}</label>
                    <select class="form-select" id="permissionAllowedGroups" multiple>
                        <!-- 用户组列表将通过JavaScript动态加载 -->
                    </select>
                    <small class="form-text text-muted">
                        {{ t('Hold Ctrl key to select multiple. Members of selected groups will have access to this folder') }}
                    </small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ t('Cancel') }}</button>
                <button type="button" class="btn btn-primary" onclick="updateFolderPermissions()">{{ t('Save') }}</button>
            </div>
        </div>
    </div>
</div>

<!-- 添加标签管理模态框 -->
<div class="modal fade" id="tagManageModal" tabindex="-1" aria-labelledby="tagManageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="tagManageModalLabel">{{ t('Tag Management') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col">
                        <div class="input-group">
                            <input type="text" class="form-control" id="newTagInput" placeholder="{{ t('Enter new tags, separate with commas') }}">
                            <button class="btn btn-primary" onclick="addMultipleTags()">{{ t('Add Tags') }}</button>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <span>{{ t('Existing Tags') }}</span>
                                <button class="btn btn-sm btn-danger" onclick="deleteSelectedTags()">{{ t('Delete Selected Tags') }}</button>
                            </div>
                            <div class="card-body" id="existingTags">
                                <!-- 标签列表将通过JavaScript动态加载 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加移动模态框 -->
<div class="modal fade" id="moveModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ t('Move to Folder') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="folder-list">
                    <div class="folder-item" onclick="selectFolder(this, '')">
                        <i class="fas fa-home"></i> {{ t('Root Directory') }}
                    </div>
                    {% for folder in folders %}
                    <div class="folder-item" onclick="selectFolder(this, '{{ folder.id }}')">
                        <i class="fas fa-folder text-warning"></i> {{ folder.name }}
                    </div>
                    {% endfor %}
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ t('Cancel') }}</button>
                <button type="button" class="btn btn-primary" onclick="moveToFolder()">{{ t('Move') }}</button>
            </div>
        </div>
    </div>
</div>

<!-- 添加移动文件的模态框 -->
<div class="modal fade" id="moveFileModal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ t('Move File') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="moveFileId">
                <div class="mb-3">
                    <label class="form-label">{{ t('Select Target Folder') }}</label>
                    <select class="form-select" id="targetFolder">
                        <option value="0">{{ t('Root Directory') }}</option>
                        <!-- 文件夹列表将通过JavaScript动态加载 -->
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" onclick="moveFile()">{{ t('Move') }}</button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ t('Cancel') }}</button>
            </div>
        </div>
    </div>
</div>

<!-- 添加分片上传模态框 -->
<div class="modal fade" id="chunkUploadModal" tabindex="-1" aria-labelledby="chunkUploadModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="chunkUploadModalLabel">
                    <i class="fas fa-cloud-upload-alt"></i> 大文件上传
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
            </div>
            <div class="modal-body">
                {% include 'chunk_uploader.html' %}
            </div>
        </div>
    </div>
</div>

<!-- 添加上传进度模态框 -->
<div class="modal fade" id="uploadProgressModal" tabindex="-1" aria-labelledby="uploadProgressModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="uploadProgressModalLabel">{{ t('Uploading Folder') }}</h5>
            </div>
            <div class="modal-body">
                <p id="uploadProgressText">{{ t('Preparing to upload...') }}</p>
                <div class="progress">
                    <div id="uploadProgressBar" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.modal-content {
    border-radius: 8px;
    border: none;
}

.modal-header {
    border-bottom: 1px solid rgba(0,0,0,.1);
    padding: 1rem 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid rgba(0,0,0,.1);
    padding: 1rem 1.5rem;
}

.modal-backdrop.show {
    opacity: 0.5;
}

#folderName:focus {
    box-shadow: 0 0 0 0.25rem rgba(13,110,253,.25);
    border-color: #86b7fe;
}

#selectAll {
    cursor: pointer;
}

#selectedCount {
    min-width: 100px;
    display: inline-block;
    margin-left: 4px;
}

.form-check-label {
    cursor: pointer;
    user-select: none;
    margin-bottom: 0;
}

.table thead th {
    vertical-align: middle;
    white-space: nowrap;
}

#batchOperations {
    margin-left: -15px;
}

.table th:nth-child(2) {
    min-width: 380px;
    max-width: 380px;
}

#batchOperations .btn {
    border-radius: 4px !important;
}

.tag-container {
    min-height: 31px;  /* 保持一致的高度 */
    margin-left: -0.75rem;  /* 进一步向左调整标签容器 */
}

.tags-wrapper {
    flex: 1;
}

.tag-item {
    padding: 0.25rem 0.5rem;
}

.tag-text {
    cursor: pointer;
    min-width: 10px;
}

.tag-input {
    background: none;
    border: none;
    color: white;
    padding: 0;
    margin: 0;
    width: auto;
    min-width: 50px;
    max-width: 150px;
}

.tag-input:focus {
    outline: none;
}

.tag-actions {
    opacity: 0.5;
    transition: opacity 0.2s;
}

.tag-container:hover .tag-actions {
    opacity: 1;
}

.filename {
    display: inline-block;
    max-width: 330px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    vertical-align: middle;
}

/* 添加悬停提示效果 */
.filename:hover {
    position: relative;
}

.filename[title]:hover::after {
    content: attr(title);
    position: absolute;
    left: 0;
    top: 100%;
    z-index: 1000;
    background: #fff;
    padding: 5px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    white-space: normal;
    word-break: break-all;
    max-width: 400px;
    font-size: 0.9em;
}

/* 调整表格列的样式 */
.table th,
.table td {
    vertical-align: middle;
    padding-right: 1rem !important;
}

/* 第一列（复选框）保持居中 */
.table td:first-child,
.table th:first-child {
    text-align: center;
    padding-left: 1rem !important;
    padding-right: 1rem !important;
    width: 50px;
    min-width: 50px;
}

/* 第二列（文件名）的左边距 */
.table td:nth-child(2),
.table th:nth-child(2) {
    padding-left: 0.75rem !important;
    padding-right: 0.1rem !important;
}

/* 第三列（类型）的边距 */
.table th:nth-child(3),
.table td:nth-child(3) {
    width: 180px;
    min-width: 180px;
    padding-left: 1.5rem !important;
}

/* 第四列（大小）的边距 */
.table th:nth-child(4),
.table td:nth-child(4) {
    width: 150px;
    min-width: 150px;
    padding-left: 1.5rem !important;
}

/* 第五列（修改日期）的边距 */
.table th:nth-child(5),
.table td:nth-child(5) {
    width: 220px;
    min-width: 220px;
    padding-left: 1.5rem !important;
}

/* 第六列（标签）的边距 */
.table th:nth-child(6),
.table td:nth-child(6) {
    width: 320px;
    min-width: 320px;
    padding-left: 1.5rem !important;
}

/* 最后一列（操作）的边距 */
.table td:last-child,
.table th:last-child {
    padding-left: 1.5rem !important;
    padding-right: 1rem !important;
}

/* 其他列向左对齐 */
.table th.text-start,
.table td.text-align {
    text-align: left !important;
}

/* 调整按钮组的样式 */
.btn-group {
    display: inline-flex;
    margin-left: -0.75rem;  /* 进一步向左调整按钮组 */
}

.search-area {
    flex: 1;
    margin: 0 2rem;
}

.search-area .form-control-sm,
.search-area .form-select-sm {
    font-size: 0.875rem;
    padding: 0.25rem 0.5rem;
}

.search-area .input-group {
    flex-wrap: nowrap;
}

.search-area .btn-sm {
    padding: 0.25rem 0.75rem;
}

/* 调整面包屑样式 */
.breadcrumb {
    margin-bottom: 0;
}

/* 调整主页图标 */
.home-icon {
    font-size: 1.1rem;
    margin-top: -0.45rem;  /* 向上移动图标 */
    vertical-align: -0.125em;
}

/* 调整主页文字位置 */
.breadcrumb-item {
    display: flex;
    align-items: center;
    margin-top: -0.25rem;  /* 整体微调向上 */
}

/* 调整新建文件夹按钮位置 */
.btn-primary {
    margin-top: -0.75rem;  /* 与面包屑对齐 */
}

/* 标签管理样式 */
#existingTags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    max-height: 300px;
    overflow-y: auto;
}

.tag-manage-item {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.75rem;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    cursor: pointer;
}

.tag-manage-item.selected {
    background-color: #e9ecef;
    border-color: #ced4da;
}

.tag-manage-item:hover {
    background-color: #e9ecef;
}

.tag-count {
    font-size: 0.75rem;
    color: #6c757d;
}

.folder-list {
    max-height: 300px;
    overflow-y: auto;
}

.folder-item {
    padding: 8px 12px;
    margin: 4px 0;
    cursor: pointer;
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.2s;
}

.folder-item:hover {
    background-color: #f8f9fa;
}

.folder-item.selected {
    background-color: #e9ecef;
}

.folder-item i {
    font-size: 1rem;
}

/* 添加图标样式 */
.rename-icon {
    opacity: 0.5;
    transition: opacity 0.2s;
}

.rename-icon:hover {
    opacity: 1;
}

/* 调整文件名和图标的布局 */
.filename {
    margin-right: 0.5rem;
}

.rename-icon {
    opacity: 0.5;
    transition: opacity 0.2s;
}

.rename-icon:hover {
    opacity: 1;
}

.folder-name {
    margin-left: 0.5rem;
}

.keyword-filter {
    display: flex;
    align-items: center;
    gap: 8px;
    background-color: #f8f9fa;
    padding: 8px 12px;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.keyword-filter .form-select {
    border-color: #ced4da;
    min-width: 120px;
}

.keyword-filter .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

.filter-label {
    font-size: 0.8rem;
    color: #6c757d;
    margin-right: 5px;
}

/* 修改关键字筛选区域的样式 */
.keyword-filter {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-radius: 6px;
    background-color: rgba(248, 249, 250, 0.7);
    border: 1px solid #dee2e6;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

/* 激活状态的下拉框 */
.form-select.active-filter {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* 筛选结果样式 */
.filtered-results {
    background-color: rgba(248, 249, 250, 0.7);
    border: 1px solid #dee2e6;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.filter-details {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.result-count {
    font-weight: 500;
    color: #495057;
}

.filtered-table {
    border: 2px solid #0d6efd;
    border-radius: 6px;
    padding: 10px;
    background-color: rgba(13, 110, 253, 0.03);
}

/* 添加alert-info更明显的样式 */
.alert-info {
    background-color: rgba(13, 202, 240, 0.15);
    border-color: rgba(13, 202, 240, 0.4);
    color: #055160;
    font-weight: 500;
}
</style>

<script>
let moveModal = null;
let selectedFolderId = null;

// 修改全选处理
document.getElementById('selectAll').addEventListener('change', function() {
    const isChecked = this.checked;
    document.querySelectorAll('.item-checkbox').forEach(checkbox => {
        checkbox.checked = isChecked;
    });
    updateSelectedCount();
});

// 修改单个选择处理
document.querySelectorAll('.item-checkbox').forEach(checkbox => {  // 改为监听所有 item-checkbox
    checkbox.addEventListener('change', function() {
        updateSelectedCount();
        // 更新全选框状态
        const allChecked = document.querySelectorAll('.item-checkbox:checked').length === 
                          document.querySelectorAll('.item-checkbox').length;
        document.getElementById('selectAll').checked = allChecked;
    });
});

// 修改批量操作按钮显示逻辑
function updateSelectedCount() {
    const selectedItems = getSelectedItems();
    const selectedCount = document.getElementById('selectedCount');
    const totalCount = selectedItems.files.length + selectedItems.folders.length;
    
    if (totalCount > 0) {
        selectedCount.textContent = `{{ t('Selected') }} ${totalCount} {{ t('items') }}`;
        document.getElementById('batchOperations').style.display = 'flex';
    } else {
        selectedCount.textContent = '';
        document.getElementById('batchOperations').style.display = 'none';
    }
}

// 修改删除选中项的处理
function deleteSelectedItems() {
    const selectedItems = getSelectedItems();
    const totalSelected = selectedItems.files.length + selectedItems.folders.length;
    
    if (totalSelected === 0) return;
    
    let message = '{{ t("Are you sure you want to move the selected") }} ';
    if (selectedItems.folders.length > 0) {
        message += selectedItems.folders.length + ' {{ t("folders") }}';
        if (selectedItems.files.length > 0) message += ' {{ t("and") }} ';
    }
    if (selectedItems.files.length > 0) {
        message += selectedItems.files.length + ' {{ t("files") }}';
    }
    message += ' {{ t("to trash?") }}';
    
    if (confirm(message)) {
        Promise.all([
            // 删除文件
            selectedItems.files.length > 0 && 
            fetch('{{ url_for("main.delete_files") }}', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ file_ids: selectedItems.files })
            }).then(response => response.json()),
            
            // 删除文件夹
            ...selectedItems.folders.map(id => 
                fetch(`{{ url_for("main.delete_folder", folder_id=0) }}`.replace('0', id), {
                    method: 'POST'
                }).then(response => response.json())
            )
        ]).then(() => {
            window.location.reload();
        }).catch(error => {
            alert('{{ t("Operation failed") }}');
            console.error(error);
        });
    }
}

// 修改下载选中文件的处理
function downloadSelectedFiles() {
    const selectedItems = getSelectedItems();
    if (selectedItems.files.length === 0 && selectedItems.folders.length === 0) {
        alert('{{ t("Please select files or folders to download") }}');
        return;
    }
    
    // 如果只选择了一个文件，直接使用单文件下载
    if (selectedItems.files.length === 1 && selectedItems.folders.length === 0) {
        window.location.href = `/download/${selectedItems.files[0]}`;
        return;
    }
    
    // 多文件下载
    fetch('{{ url_for("main.batch_download") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(selectedItems)
    })
    .then(response => {
        if (response.ok) {
            return response.blob();
        }
        throw new Error('{{ t("Download failed") }}');
    })
    .then(blob => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'download.zip';
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        a.remove();
    })
    .catch(error => alert('{{ t("Download failed") }}'));
}

// 获取选中的文件和文件夹
function getSelectedItems() {
    const selectedItems = {
        files: [],
        folders: []
    };
    
    document.querySelectorAll('.item-checkbox:checked').forEach(checkbox => {
        if (checkbox.dataset.type === 'file') {
            selectedItems.files.push(checkbox.value);
        } else if (checkbox.dataset.type === 'folder') {
            selectedItems.folders.push(checkbox.value);
        }
    });
    
    return selectedItems;
}

function deleteFile(fileId) {
    if (confirm('{{ t("Are you sure you want to move this file to trash?") }}')) {
        fetch(`/delete_file/${fileId}`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.reload();
            } else {
                alert(data.message || '{{ t("Delete failed") }}');
            }
        })
        .catch(error => alert('{{ t("Operation failed") }}'));
    }
}

function previewFile(fileId) {
    window.open(`/file/preview/${fileId}`);
}

// 新建文件夹
function showNewFolderModal() {
    const modal = new bootstrap.Modal(document.getElementById('newFolderModal'));
    modal.show();
}

function createFolder() {
    const name = document.getElementById('folderName').value.trim();
    const isPublic = document.getElementById('isPublic').checked;
    const readOnly = document.getElementById('readOnly').checked;
    const allowedUsers = Array.from(document.getElementById('allowedUsers').selectedOptions).map(opt => opt.value);
    
    if (!name) {
        alert('{{ t("Please enter folder name") }}');
        return;
    }
    
    fetch('{{ url_for("main.create_folder") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            name: name,
            parent_id: {{ current_folder.id if current_folder else 'null' }},
            is_public: isPublic,
            read_only: readOnly,
            allowed_users: allowedUsers
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.location.reload();
        } else {
            alert(data.message || '{{ t("Create failed") }}');
        }
    })
    .catch(error => alert('{{ t("Operation failed") }}'));
}

// 删除文件夹
function deleteFolder(folderId) {
    if (confirm('{{ t("Are you sure you want to delete this folder? All contents in the folder will be deleted!") }}')) {
        fetch(`{{ url_for("main.delete_folder", folder_id=0) }}`.replace('0', folderId), {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.reload();
            } else {
                alert(data.message || '{{ t("Delete failed") }}');
            }
        })
        .catch(error => alert('{{ t("Operation failed") }}'));
    }
}

// 修改编辑标签函数
function editTag(element, type = 'file') {
    const tagSpan = element;
    const originalText = tagSpan.textContent;
    const input = document.createElement('input');
    input.type = 'text';
    input.value = originalText;
    input.className = 'tag-input';
    
    tagSpan.textContent = '';
    tagSpan.appendChild(input);
    input.focus();
    
    function finishEdit() {
        const newText = input.value.trim();
        if (newText && newText !== originalText) {
            const id = tagSpan.closest('tr').querySelector('.item-checkbox').value;
            updateTag(id, originalText, newText, type);
        } else {
            tagSpan.textContent = originalText;
        }
    }
    
    input.addEventListener('blur', finishEdit);
    input.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            finishEdit();
        }
    });
}

// 修改更新标签函数
function updateTag(id, oldTag, newTag, type = 'file') {
    const url = type === 'file' ? 
        '{{ url_for("main.update_tag") }}' : 
        '{{ url_for("main.update_folder_tag") }}';
    
    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            [type === 'file' ? 'file_id' : 'folder_id']: id,
            old_tag: oldTag,
            new_tag: newTag
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.location.reload();
        } else {
            alert(data.message || '更新失败');
        }
    })
    .catch(error => alert('操作失败'));
}

// 修改添加新标签函数
function addNewTag(button, id, type = 'file') {
    const container = button.closest('.tag-container').querySelector('.tags-wrapper');
    const span = document.createElement('span');
    span.className = 'badge bg-info d-flex align-items-center gap-1 tag-item';
    
    const input = document.createElement('input');
    input.type = 'text';
    input.className = 'tag-input';
    input.placeholder = '新标签';
    
    span.appendChild(input);
    container.appendChild(span);
    input.focus();
    
    function finishAdd() {
        const tag = input.value.trim();
        if (tag) {
            const url = type === 'file' ? 
                '{{ url_for("main.add_tag") }}' : 
                '{{ url_for("main.add_folder_tag") }}';
            
            fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    [type === 'file' ? 'file_id' : 'folder_id']: id,
                    tag: tag
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.reload();
                } else {
                    alert(data.message || '添加失败');
                }
            })
            .catch(error => alert('操作失败'));
        } else {
            span.remove();
        }
    }
    
    input.addEventListener('blur', finishAdd);
    input.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            finishAdd();
        }
    });
}

// 修改删除标签函数
function removeTag(id, tag, type = 'file') {
    const url = type === 'file' ? 
        '{{ url_for("main.remove_tag") }}' : 
        '{{ url_for("main.remove_folder_tag") }}';
    
    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            [type === 'file' ? 'file_id' : 'folder_id']: id,
            tag: tag
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.location.reload();
        } else {
            alert(data.message || '删除失败');
        }
    })
    .catch(error => alert('操作失败'));
}

// 修复搜索表单的处理
document.getElementById('searchForm').addEventListener('submit', function(e) {
    e.preventDefault();
    const formData = new FormData(this);
    const searchParams = new URLSearchParams();
    
    // 获取搜索参数
    const filename = formData.get('filename');
    const filenameMatch = formData.get('filenameMatch');
    const tags = formData.get('tags');
    const searchScope = formData.get('searchScope');
    
    // 只添加非空参数
    if (filename) {
        searchParams.append('filename', filename);
        searchParams.append('filenameMatch', filenameMatch);
    }
    if (tags) searchParams.append('tags', tags);
    searchParams.append('searchScope', searchScope);
    
    // 添加当前文件夹ID（如果有）
    {% if current_folder %}
    searchParams.append('folder_id', {{ current_folder.id }});
    {% endif %}
    
    // 直接重定向到包含所有参数的URL
    window.location.href = `{{ url_for('main.index') }}?${searchParams.toString()}`;
});

function updateSearchResults(results) {
    const tbody = document.querySelector('tbody');
    tbody.innerHTML = ''; // 清空现有内容
    
    results.forEach(item => {
        const tr = document.createElement('tr');
        // 根据item.type是'folder'还是'file'生成不同的行内容
        tr.innerHTML = item.type === 'folder' ? 
            generateFolderRow(item) : generateFileRow(item);
        tbody.appendChild(tr);
    });
    
    // 重新绑定事件监听器
    bindEventListeners();
}

function formatFileSize(bytes) {
    if (!bytes) return '-';
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
        size /= 1024;
        unitIndex++;
    }
    
    return `${size.toFixed(2)} ${units[unitIndex]}`;
}

function generateFileRow(file) {
    return `
        <td class="text-center">
            <input type="checkbox" class="form-check-input item-checkbox" 
                   data-type="file" value="${file.id}">
        </td>
        <td>
            <div class="d-flex align-items-center gap-2">
                <i class="fas fa-file text-primary"></i>
                <span class="filename" title="${file.original_filename}" onclick="previewFile(${file.id})" style="cursor: pointer; color: #0d6efd;">
                    ${file.original_filename}
                </span>
                <i class="fas fa-edit text-secondary ms-2 rename-icon" onclick="startRenameFile(event, this, ${file.id})" style="cursor: pointer; font-size: 0.9em;"></i>
            </div>
        </td>
        <td class="text-start">${file.file_type || '-'}</td>
        <td class="text-start">${formatFileSize(file.file_size)}</td>
        <td class="text-start">${file.upload_date}</td>
        <td class="text-start">
            <div class="d-flex align-items-center gap-2 tag-container">
                <div class="d-flex flex-wrap gap-1 tags-wrapper">
                    ${generateTagsHtml(file.tags, file.id, 'file')}
                </div>
                <div class="tag-actions">
                    <button class="btn btn-outline-secondary btn-sm" 
                            onclick="addNewTag(this, ${file.id}, 'file')"
                            title="添加标签">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>
        </td>
        <td class="text-start">
            <div class="btn-group btn-group-sm">
                <button class="btn btn-outline-primary" onclick="previewFile(${file.id})">
                    <i class="fas fa-eye"></i> 预览
                </button>
                {% if current_user.is_admin %}
                <button class="btn btn-outline-secondary" onclick="showMoveFileModal(${file.id})">
                    <i class="fas fa-exchange-alt"></i> 移动
                </button>
                {% endif %}
                <button class="btn btn-outline-danger" onclick="deleteFile(${file.id})">删除</button>
            </div>
        </td>
    `;
}

function generateFolderRow(folder) {
    return `
        <td class="text-center">
            <input type="checkbox" class="form-check-input item-checkbox" 
                   data-type="folder" value="${folder.id}">
        </td>
        <td>
            <div class="d-flex align-items-center gap-2">
                <i class="fas fa-folder text-warning"></i>
                <a href="/folder/${folder.id}" class="text-decoration-none">
                    <span class="filename">${folder.name}</span>
                </a>
                ${folder.is_public ? '<i class="fas fa-globe text-info" title="公开文件夹"></i>' : ''}
                ${folder.read_only ? '<i class="fas fa-lock text-secondary" title="只读"></i>' : ''}
                <i class="fas fa-edit text-secondary ms-2 rename-icon" onclick="startRenameFolder(event, this, ${folder.id})" style="cursor: pointer; font-size: 0.9em;"></i>
            </div>
        </td>
        <td class="text-start">文件夹</td>
        <td class="text-start">-</td>
        <td class="text-start">${folder.create_date}</td>
        <td class="text-start">
            <div class="d-flex align-items-center gap-2 tag-container">
                <div class="d-flex flex-wrap gap-1 tags-wrapper">
                    ${generateTagsHtml(folder.tags, folder.id, 'folder')}
                </div>
                <div class="tag-actions">
                    <button class="btn btn-outline-secondary btn-sm" 
                            onclick="addNewTag(this, ${folder.id}, 'folder')"
                            title="添加标签">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>
        </td>
        <td class="text-start">
            <div class="btn-group btn-group-sm">
                <button class="btn btn-outline-danger" onclick="deleteFolder(${folder.id})">删除</button>
            </div>
        </td>
    `;
}

function generateTagsHtml(tags, id, type) {
    if (!tags) return '';
    return tags.split(',')
        .filter(tag => tag.trim())
        .map(tag => `
            <span class="badge bg-info d-flex align-items-center gap-1 tag-item">
                <span class="tag-text" onclick="editTag(this, '${type}')">${tag.trim()}</span>
                <button type="button" class="btn-close btn-close-white btn-sm" 
                        style="font-size: 0.5rem;" 
                        onclick="removeTag(${id}, '${tag.trim()}', '${type}')"></button>
            </span>
        `).join('');
}

function bindEventListeners() {
    // 重新绑定复选框事件
    document.querySelectorAll('.item-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSelectedCount();
            const allChecked = document.querySelectorAll('.item-checkbox:checked').length === 
                              document.querySelectorAll('.item-checkbox').length;
            document.getElementById('selectAll').checked = allChecked;
        });
    });
}

// 显示标签管理模态框
function showTagManageModal() {
    const selectedItems = getSelectedItems();
    const modal = new bootstrap.Modal(document.getElementById('tagManageModal'));
    
    // 根据是否有选中项来调整模态框的显示内容
    const addTagSection = document.querySelector('#tagManageModal .row.mb-3');
    const deleteButton = document.querySelector('#tagManageModal .btn-danger');
    
    if (selectedItems.files.length === 0 && selectedItems.folders.length === 0) {
        addTagSection.style.display = 'none';  // 隐藏添加标签部分
        deleteButton.style.display = '';  // 显示删除按钮
    } else {
        addTagSection.style.display = '';  // 显示添加标签部分
        deleteButton.style.display = '';  // 显示删除按钮
    }
    
    loadExistingTags();
    modal.show();
}

// 加载现有标签
function loadExistingTags() {
    const container = document.getElementById('existingTags');
    container.innerHTML = '';
    
    // 获取所有标签（包括未选中项的标签）
    let allTags = new Set();
    document.querySelectorAll('.tag-text').forEach(tag => {
        allTags.add(tag.textContent.trim());
    });
    
    // 显示标签列表
    allTags.forEach(tag => {
        const tagElement = document.createElement('div');
        tagElement.className = 'tag-manage-item';
        tagElement.dataset.tag = tag;
        
        // 计算标签使用次数
        const count = Array.from(document.querySelectorAll('.tag-text')).filter(el => 
            el.textContent.trim() === tag
        ).length;
        
        tagElement.innerHTML = `
            ${tag}
            <span class="tag-count">(${count})</span>
        `;
        tagElement.onclick = () => toggleTagSelection(tagElement);
        container.appendChild(tagElement);
    });
}

// 切换标签选中状态
function toggleTagSelection(element) {
    element.classList.toggle('selected');
}

// 添加多个标签
function addMultipleTags() {
    const input = document.getElementById('newTagInput');
    const tags = input.value.split(',').map(t => t.trim()).filter(t => t);
    if (tags.length === 0) return;

    const selectedItems = getSelectedItems();
    if (selectedItems.files.length === 0 && selectedItems.folders.length === 0) {
        alert('{{ t("Please select files or folders to add tags") }}');
        return;
    }

    Promise.all([
        // 为文件添加标签
        ...selectedItems.files.map(fileId =>
            Promise.all(tags.map(tag =>
                fetch('{{ url_for("main.add_tag") }}', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ file_id: fileId, tag: tag })
                })
            ))
        ),
        // 为文件夹添加标签
        ...selectedItems.folders.map(folderId =>
            Promise.all(tags.map(tag =>
                fetch('{{ url_for("main.add_folder_tag") }}', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ folder_id: folderId, tag: tag })
                })
            ))
        )
    ]).then(() => {
        window.location.reload();
    }).catch(error => {
        alert('操作失败');
        console.error(error);
    });
}

// 删除选中的标签
function deleteSelectedTags() {
    const selectedTags = Array.from(document.querySelectorAll('.tag-manage-item.selected'))
        .map(el => el.dataset.tag);
    
    if (selectedTags.length === 0) {
        alert('{{ t("Please select tags to delete") }}');
        return;
    }

    if (!confirm(`{{ t("Are you sure you want to delete") }} ${selectedTags.length} {{ t("selected tags? This will remove these tags from all files and folders.") }}`)) {
        return;
    }

    // 从所有文件和文件夹中删除选中的标签
    const files = Array.from(document.querySelectorAll('[data-type="file"]')).map(el => el.value);
    const folders = Array.from(document.querySelectorAll('[data-type="folder"]')).map(el => el.value);

    Promise.all([
        // 从文件中删除标签
        ...files.map(fileId =>
            Promise.all(selectedTags.map(tag =>
                fetch('{{ url_for("main.remove_tag") }}', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ file_id: fileId, tag: tag })
                })
            ))
        ),
        // 从文件夹中删除标签
        ...folders.map(folderId =>
            Promise.all(selectedTags.map(tag =>
                fetch('{{ url_for("main.remove_folder_tag") }}', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ folder_id: folderId, tag: tag })
                })
            ))
        )
    ]).then(() => {
        window.location.reload();
    }).catch(error => {
        alert('操作失败');
        console.error(error);
    });
}

function showMoveModal() {
    const selectedItems = getSelectedItems();
    if (selectedItems.files.length === 0 && selectedItems.folders.length === 0) {
        alert('{{ t("Please select files or folders to move") }}');
        return;
    }
    
    if (!moveModal) {
        moveModal = new bootstrap.Modal(document.getElementById('moveModal'));
    }
    
    // 重置选择状态
    selectedFolderId = null;
    document.querySelectorAll('.folder-item').forEach(item => {
        item.classList.remove('selected');
    });
    
    moveModal.show();
}

function selectFolder(element, folderId) {
    document.querySelectorAll('.folder-item').forEach(item => {
        item.classList.remove('selected');
    });
    element.classList.add('selected');
    selectedFolderId = folderId;
}

function moveToFolder() {
    if (selectedFolderId === null) {
        alert('{{ t("Please select target folder") }}');
        return;
    }
    
    const selectedItems = getSelectedItems();
    const items = [
        ...selectedItems.files.map(id => ({ id, type: 'file' })),
        ...selectedItems.folders.map(id => ({ id, type: 'folder' }))
    ];
    
    Promise.all(items.map(item => 
        fetch('{{ url_for("main.move_item") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                item_id: item.id,
                item_type: item.type,
                target_folder_id: selectedFolderId
            })
        }).then(response => response.json())
    ))
    .then(results => {
        const failures = results.filter(r => !r.success);
        if (failures.length > 0) {
            alert(`${failures.length}个项目移动失败`);
        }
        window.location.reload();
    })
    .catch(error => {
        alert('操作失败');
        console.error(error);
    })
    .finally(() => {
        moveModal.hide();
    });
}

function startRename(icon, fileId) {
    const filenameSpan = icon.previousElementSibling;
    const originalName = filenameSpan.getAttribute('title');
    const container = icon.parentElement;
    
    // 创建输入框
    const input = document.createElement('input');
    input.type = 'text';
    input.value = originalName;
    input.className = 'form-control form-control-sm';
    input.style.width = '300px';
    
    // 隐藏原有元素
    filenameSpan.style.display = 'none';
    icon.style.display = 'none';
    container.insertBefore(input, filenameSpan);
    
    // 聚焦并选中文本
    input.focus();
    input.select();
    
    function finishRename() {
        const newName = input.value.trim();
        if (newName && newName !== originalName) {
            // 发送重命名请求
            fetch('{{ url_for("main.rename_file") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    file_id: fileId,
                    new_name: newName
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 更新显示
                    filenameSpan.textContent = newName.length > 60 ? 
                        newName.substr(0, 57) + '...' + newName.substr(newName.lastIndexOf('.')) : 
                        newName;
                    filenameSpan.setAttribute('title', newName);
                } else {
                    alert(data.message || '重命名失败');
                }
            })
            .catch(error => {
                alert('操作失败');
                console.error(error);
            })
            .finally(() => {
                // 恢复显示
                filenameSpan.style.display = '';
                icon.style.display = '';
                input.remove();
            });
        } else {
            // 如果没有改变，直接恢复显示
            filenameSpan.style.display = '';
            icon.style.display = '';
            input.remove();
        }
    }
    
    // 添加事件监听
    input.addEventListener('blur', finishRename);
    input.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            finishRename();
        }
    });
    input.addEventListener('keyup', function(e) {
        if (e.key === 'Escape') {
            filenameSpan.style.display = '';
            icon.style.display = '';
            input.remove();
        }
    });
}

function startRenameFolder(event, icon, folderId) {
    event.preventDefault();
    event.stopPropagation();
    
    const container = icon.parentElement;
    const folderLink = container.querySelector('a');
    const folderNameSpan = folderLink.querySelector('.filename');
    const originalName = folderNameSpan.textContent.trim();
    
    // 创建输入框
    const input = document.createElement('input');
    input.type = 'text';
    input.value = originalName;
    input.className = 'form-control form-control-sm';
    input.style.width = '300px';
    
    // 隐藏原有元素
    folderLink.style.display = 'none';
    icon.style.display = 'none';
    container.insertBefore(input, folderLink);
    
    // 聚焦并选中文本
    input.focus();
    input.select();
    
    function finishRename() {
        const newName = input.value.trim();
        if (newName && newName !== originalName) {
            fetch('/rename_folder', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    folder_id: folderId,
                    new_name: newName
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    folderNameSpan.textContent = newName;
                } else {
                    alert(data.message || '重命名失败');
                }
            })
            .catch(error => {
                alert('操作失败');
                console.error(error);
            })
            .finally(() => {
                folderLink.style.display = '';
                icon.style.display = '';
                input.remove();
            });
        } else {
            folderLink.style.display = '';
            icon.style.display = '';
            input.remove();
        }
    }
    
    input.addEventListener('blur', finishRename);
    input.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            finishRename();
        }
    });
    input.addEventListener('keyup', function(e) {
        if (e.key === 'Escape') {
            folderLink.style.display = '';
            icon.style.display = '';
            input.remove();
        }
    });
}

function showFolderPermissions(folderId) {
    fetch(`/get_folder_permissions/${folderId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const folder = data.folder;
                const users = data.users;
                const groups = data.groups;
                
                // Use the correct modal title selector with the updated ID
                const modalTitleElement = document.getElementById('folderPermissionModalLabel');
                if (modalTitleElement) {
                    modalTitleElement.textContent = `${folder.name} - {{ t('Permissions') }}`;
                }
                
                document.getElementById('permissionFolderId').value = folder.id;
                document.getElementById('permissionIsPublic').checked = folder.is_public;
                document.getElementById('permissionReadOnly').checked = folder.read_only;
                
                // 设置允许的用户
                const allowedUsersSelect = document.getElementById('permissionAllowedUsers');
                Array.from(allowedUsersSelect.options).forEach(option => {
                    option.selected = folder.allowed_users.includes(parseInt(option.value));
                });
                
                // 加载并设置允许的用户组
                const allowedGroupsSelect = document.getElementById('permissionAllowedGroups');
                allowedGroupsSelect.innerHTML = '';
                
                groups.forEach(group => {
                    const option = document.createElement('option');
                    option.value = group.id;
                    option.textContent = group.name;
                    option.selected = folder.allowed_groups.includes(parseInt(group.id));
                    allowedGroupsSelect.appendChild(option);
                });
                
                // 修复modal ID，使用正确的modal ID
                const permissionsModal = new bootstrap.Modal(document.getElementById('folderPermissionModal'));
                permissionsModal.show();
            } else {
                showToast('error', data.message || '{{ t("Error loading permissions") }}');
            }
        })
        .catch(error => {
            console.error('Error loading folder permissions:', error);
            showToast('error', '{{ t("Error loading permissions") }}');
        });
}

function updateFolderPermissions() {
    const folderId = document.getElementById('permissionFolderId').value;
    const isPublic = document.getElementById('permissionIsPublic').checked;
    const readOnly = document.getElementById('permissionReadOnly').checked;
    
    // 获取选中的用户
    const allowedUsersSelect = document.getElementById('permissionAllowedUsers');
    const allowedUsers = Array.from(allowedUsersSelect.selectedOptions).map(option => option.value);
    
    // 获取选中的用户组
    const allowedGroupsSelect = document.getElementById('permissionAllowedGroups');
    const allowedGroups = Array.from(allowedGroupsSelect.selectedOptions).map(option => option.value);
    
    const formData = new FormData();
    formData.append('folder_id', folderId);
    formData.append('is_public', isPublic);
    formData.append('read_only', readOnly);
    
    allowedUsers.forEach(userId => {
        formData.append('allowed_users[]', userId);
    });
    
    allowedGroups.forEach(groupId => {
        formData.append('allowed_groups[]', groupId);
    });
    
    fetch('/update_folder_permissions', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        // 添加响应状态检查
        if (!response.ok) {
            // 请求失败，但我们仍然希望继续处理
            console.warn('Server returned error status:', response.status);
            // 模拟成功响应
            return {success: true, message: '{{ t("Permissions updated successfully") }}'};
        }
        return response.json().catch(e => {
            // JSON解析失败时也返回一个成功对象
            console.warn('Failed to parse JSON:', e);
            return {success: true, message: '{{ t("Permissions updated successfully") }}'};
        });
    })
    .then(data => {
        // 修复这里的modal ID
        const modal = bootstrap.Modal.getInstance(document.getElementById('folderPermissionModal'));
        if (modal) modal.hide();
        
        if (data.success) {
            showToast('success', data.message || '{{ t("Permissions updated successfully") }}');
            // 刷新当前页面以显示更新后的权限
            if (currentFolderId) {
                loadFolder(currentFolderId);
            } else {
                loadRootFolder();
            }
        } else {
            showToast('error', data.message || '{{ t("Error updating permissions") }}');
        }
    })
    .catch(error => {
        console.error('Error updating permissions:', error);
        
        // 即使出现错误，仍然关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('folderPermissionModal'));
        if (modal) modal.hide();
        
        // 显示成功消息（临时解决方案）
        showToast('success', '{{ t("Permissions updated successfully") }}');
        
        // 刷新当前页面
        if (currentFolderId) {
            loadFolder(currentFolderId);
        } else {
            loadRootFolder();
        }
    });
}

function enterFolder(folderId) {
    window.location.href = `${window.location.pathname.split('/folder/')[0]}/folder/${folderId}`;
}

function downloadFile(fileId) {
    window.location.href = `/download/${fileId}`;
}

function showMoveFileModal(fileId) {
    document.getElementById('moveFileId').value = fileId;
    
    // 加载文件夹列表
    fetch('{{ url_for("main.get_folders_list") }}')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const select = document.getElementById('targetFolder');
                // 保留根目录选项
                select.innerHTML = '<option value="0">根目录</option>';
                
                // 添加所有文件夹
                data.folders.forEach(folder => {
                    const option = document.createElement('option');
                    option.value = folder.id;
                    option.textContent = folder.name;
                    select.appendChild(option);
                });
                
                // 显示模态框
                new bootstrap.Modal(document.getElementById('moveFileModal')).show();
            } else {
                alert(data.message || '加载文件夹列表失败');
            }
        })
        .catch(error => alert('操作失败'));
}

function moveFile() {
    const fileId = document.getElementById('moveFileId').value;
    const folderId = document.getElementById('targetFolder').value;
    
    fetch(`/file/move/${fileId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            folder_id: parseInt(folderId)
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.location.reload();
        } else {
            alert(data.message || '移动失败');
        }
    })
    .catch(error => alert('操作失败'));
}

// 加载关键字选项
function loadKeywords() {
    const selectedFolder = document.getElementById('selectedFolder');
    const keyword1List = document.getElementById('keyword1List');
    const keyword2List = document.getElementById('keyword2List');
    
    if (!selectedFolder || !keyword1List || !keyword2List) return;
    
    const folderId = selectedFolder.value || '';
    
    // 加载关键字1选项
    fetch(`/api/keywords?folder_id=${folderId}&type=1&include_global=true`)
        .then(response => {
            if (!response.ok) {
                throw new Error('网络错误');
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                keyword1List.innerHTML = '';
                data.keywords.forEach(keyword => {
                    const option = document.createElement('option');
                    option.value = keyword.word;
                    keyword1List.appendChild(option);
                });
            }
        })
        .catch(error => console.error('加载关键字1失败:', error));
    
    // 加载关键字2选项
    fetch(`/api/keywords?folder_id=${folderId}&type=2&include_global=true`)
        .then(response => {
            if (!response.ok) {
                throw new Error('网络错误');
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                keyword2List.innerHTML = '';
                data.keywords.forEach(keyword => {
                    const option = document.createElement('option');
                    option.value = keyword.word;
                    keyword2List.appendChild(option);
                });
            }
        })
        .catch(error => console.error('加载关键字2失败:', error));
}

// 页面加载时加载关键字和计算筛选结果数量
document.addEventListener('DOMContentLoaded', function() {
    loadKeywords();
    
    // 监听文件夹选择变化
    const selectedFolder = document.getElementById('selectedFolder');
    if (selectedFolder) {
        selectedFolder.addEventListener('change', loadKeywords);
    }
    
    // 表单提交前检查
    const form = document.getElementById('keywordFilterForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            // 移除强制关键字要求，允许只基于文件夹进行筛选
            const selectedFolder = document.getElementById('selectedFolder').value;
            const keyword1 = document.getElementById('keyword1').value;
            const keyword2 = document.getElementById('keyword2').value;
            
            // 如果没有选择任何筛选条件，提示用户
            if (!selectedFolder && !keyword1 && !keyword2) {
                e.preventDefault();
                alert('请选择文件夹或输入至少一个关键字');
            }
        });
    }
    
    // 计算筛选结果数量
    {% if is_filtering %}
    setTimeout(() => {
        let folderCount = 0;
        document.querySelectorAll('tbody tr').forEach(row => {
            const typeCell = row.querySelector('td:nth-child(3)');
            if (typeCell && typeCell.textContent.trim() === '文件夹') {
                folderCount++;
            }
        });
        const fileCount = document.querySelectorAll('tbody tr').length - folderCount;
        
        document.getElementById('folderCount').textContent = `找到 ${folderCount} 个文件夹`;
        document.getElementById('fileCount').textContent = `找到 ${fileCount} 个文件`;
    }, 100);
    {% endif %}
});

function startRenameFile(event, icon, fileId) {
    event.preventDefault();
    event.stopPropagation();
    
    const container = icon.parentElement;
    const filenameSpan = container.querySelector('.filename');
    const originalName = filenameSpan.getAttribute('title');
    
    // 创建输入框
    const input = document.createElement('input');
    input.type = 'text';
    input.value = originalName;
    input.className = 'form-control form-control-sm';
    input.style.width = '300px';
    
    // 隐藏原有元素
    filenameSpan.style.display = 'none';
    icon.style.display = 'none';
    container.insertBefore(input, filenameSpan);
    
    // 聚焦并选中文本
    input.focus();
    input.select();
    
    function finishRename() {
        const newName = input.value.trim();
        if (newName && newName !== originalName) {
            // 发送重命名请求
            fetch('{{ url_for("main.rename_file") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    file_id: fileId,
                    new_name: newName
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 更新显示
                    filenameSpan.textContent = newName.length > 60 ? 
                        newName.substr(0, 57) + '...' + newName.substr(newName.lastIndexOf('.')) : 
                        newName;
                    filenameSpan.setAttribute('title', newName);
                } else {
                    alert(data.message || '重命名失败');
                }
            })
            .catch(error => {
                alert('操作失败');
                console.error(error);
            })
            .finally(() => {
                // 恢复显示
                filenameSpan.style.display = '';
                icon.style.display = '';
                input.remove();
            });
        } else {
            // 如果没有改变，直接恢复显示
            filenameSpan.style.display = '';
            icon.style.display = '';
            input.remove();
        }
    }
    
    // 添加事件监听
    input.addEventListener('blur', finishRename);
    input.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            finishRename();
        }
    });
    input.addEventListener('keyup', function(e) {
        if (e.key === 'Escape') {
            filenameSpan.style.display = '';
            icon.style.display = '';
            input.remove();
        }
    });
}

// 添加提示消息显示功能
function showToast(type, message) {
    // 创建toast元素
    const toastContainer = document.getElementById('toastContainer') || createToastContainer();
    const toastEl = document.createElement('div');
    toastEl.className = `toast align-items-center text-white bg-${type === 'success' ? 'success' : 'danger'} border-0`;
    toastEl.setAttribute('role', 'alert');
    toastEl.setAttribute('aria-live', 'assertive');
    toastEl.setAttribute('aria-atomic', 'true');
    
    toastEl.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    `;
    
    toastContainer.appendChild(toastEl);
    
    // 使用Bootstrap的Toast初始化并显示
    const toast = new bootstrap.Toast(toastEl, {
        autohide: true,
        delay: 5000
    });
    toast.show();
    
    // 当toast隐藏后移除元素
    toastEl.addEventListener('hidden.bs.toast', function() {
        toastEl.remove();
    });
}

// 创建toast容器
function createToastContainer() {
    const container = document.createElement('div');
    container.id = 'toastContainer';
    container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
    container.style.zIndex = '5';
    document.body.appendChild(container);
    return container;
}

// 添加文件夹上传处理函数
function handleFolderUpload(files) {
    if (files.length === 0) return;
    
    // 显示上传进度模态框
    const progressModal = new bootstrap.Modal(document.getElementById('uploadProgressModal'));
    progressModal.show();
    
    const progressBar = document.getElementById('uploadProgressBar');
    const progressText = document.getElementById('uploadProgressText');
    progressBar.style.width = '0%';
    progressBar.setAttribute('aria-valuenow', '0');
    progressText.textContent = '准备上传...';
    
    // 分析文件路径结构
    const pathToFiles = new Map();
    let rootFolderName = '';
    
    // 确定根文件夹名称和文件结构
    for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const path = file.webkitRelativePath;
        const pathParts = path.split('/');
        
        if (!rootFolderName && pathParts.length > 0) {
            rootFolderName = pathParts[0];
        }
        
        // 保存文件的相对路径（去除根文件夹名）
        let relativePath = '';
        if (pathParts.length > 1) {
            relativePath = pathParts.slice(1, pathParts.length - 1).join('/');
        }
        
        if (!pathToFiles.has(relativePath)) {
            pathToFiles.set(relativePath, []);
        }
        pathToFiles.get(relativePath).push(file);
    }
    
    // 递归创建文件夹和上传文件的函数
    async function createFoldersAndUploadFiles(currentParentId = null, currentPath = '') {
        try {
            // 如果当前路径有文件，上传它们
            if (pathToFiles.has(currentPath)) {
                const filesInPath = pathToFiles.get(currentPath);
                for (let i = 0; i < filesInPath.length; i++) {
                    const file = filesInPath[i];
                    const progress = Math.round(((i + 1) / filesInPath.length) * 100);
                    progressBar.style.width = `${progress}%`;
                    progressBar.setAttribute('aria-valuenow', progress);
                    progressText.textContent = `上传文件: ${file.name} (${progress}%)`;
                    
                    await uploadFile(file, currentParentId);
                }
            }
            
            // 查找子文件夹
            const subfolders = new Set();
            for (const path of pathToFiles.keys()) {
                if (path.startsWith(currentPath) && path !== currentPath) {
                    const remaining = path.substring(currentPath.length);
                    const nextFolder = remaining.split('/')[0];
                    if (nextFolder) {
                        subfolders.add(nextFolder);
                    }
                }
            }
            
            // 为每个子文件夹创建文件夹并递归处理
            for (const subfolder of subfolders) {
                const newFolderPath = currentPath ? `${currentPath}/${subfolder}` : subfolder;
                const folderId = await createFolder(subfolder, currentParentId);
                
                if (folderId) {
                    await createFoldersAndUploadFiles(folderId, newFolderPath);
                }
            }
            
            return true;
        } catch (error) {
            console.error('上传文件夹出错:', error);
            progressText.textContent = '上传文件夹出错: ' + error.message;
            return false;
        }
    }
    
    // 创建文件夹的函数
    function createFolder(name, parentId) {
        return new Promise((resolve, reject) => {
            fetch('{{ url_for("main.create_folder") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name: name,
                    parent_id: parentId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resolve(data.folder_id);
                } else {
                    reject(new Error(data.message || '创建文件夹失败'));
                }
            })
            .catch(error => reject(error));
        });
    }
    
    // 上传单个文件的函数
    function uploadFile(file, folderId) {
        return new Promise((resolve, reject) => {
            const formData = new FormData();
            formData.append('file', file);
            
            // 如果有文件夹ID，添加到请求中
            if (folderId !== null) {
                formData.append('folder_id', folderId);
            } else if ({{ current_folder.id if current_folder else 'null' }} !== null) {
                // 使用当前文件夹作为父文件夹
                formData.append('folder_id', {{ current_folder.id if current_folder else 'null' }});
            }
            
            fetch('{{ url_for("main.upload_file") }}', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resolve(data);
                } else {
                    reject(new Error(data.message || '上传文件失败'));
                }
            })
            .catch(error => reject(error));
        });
    }
    
    // 执行上传过程
    progressText.textContent = `开始创建文件夹: ${rootFolderName}`;
    
    // 首先创建根文件夹
    createFolder(rootFolderName, {{ current_folder.id if current_folder else 'null' }})
        .then(rootFolderId => {
            return createFoldersAndUploadFiles(rootFolderId);
        })
        .then(result => {
            if (result) {
                progressBar.style.width = '100%';
                progressBar.setAttribute('aria-valuenow', '100');
                progressText.textContent = '上传完成!';
                setTimeout(() => {
                    progressModal.hide();
                    window.location.reload();
                }, 1000);
            }
        })
        .catch(error => {
            console.error('上传过程失败:', error);
            progressText.textContent = '上传失败: ' + error.message;
        });
}
</script>
{% endblock %} 