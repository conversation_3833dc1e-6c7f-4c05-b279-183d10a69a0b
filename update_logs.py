from app import create_app, db
from sqlalchemy import text

app = create_app()
with app.app_context():
    try:
        # 添加UserLog表的新列
        with db.engine.connect() as conn:
            # 检查UserLog表是否存在
            result = conn.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='user_log'"))
            if not result.fetchone():
                print("UserLog表不存在，将创建表...")
                conn.execute(text('''
                    CREATE TABLE user_log (
                        id INTEGER NOT NULL, 
                        user_id INTEGER,
                        action VARCHAR(50),
                        details TEXT,
                        timestamp DATETIME,
                        ip_address VARCHAR(50),
                        target_id INTEGER,
                        target_type VARCHAR(20),
                        PRIMARY KEY (id),
                        FOREIGN KEY(user_id) REFERENCES user (id)
                    )
                '''))
                print("UserLog表创建成功")
            else:
                print("UserLog表已存在，检查列...")
                
                # 检查列是否存在
                result = conn.execute(text("PRAGMA table_info(user_log)"))
                columns = [row[1] for row in result.fetchall()]
                
                print("现有列:", columns)
                
                # 添加缺失的列
                for column, type_def in [
                    ('timestamp', 'DATETIME'),
                    ('ip_address', 'VARCHAR(50)'),
                    ('target_id', 'INTEGER'),
                    ('target_type', 'VARCHAR(20)')
                ]:
                    if column not in columns:
                        print(f"添加列 {column}...")
                        conn.execute(text(f'ALTER TABLE user_log ADD COLUMN {column} {type_def}'))
            
            db.session.commit()
        
        print("日志表更新成功")
        
        # 显示更新后的表结构
        with db.engine.connect() as conn:
            result = conn.execute(text("PRAGMA table_info(user_log)"))
            print("\n更新后的用户日志表结构:")
            for row in result:
                print(f"字段: {row[1]}, 类型: {row[2]}, 允许空: {row[3]}, 默认值: {row[4]}")
            
    except Exception as e:
        print(f"更新失败: {str(e)}") 