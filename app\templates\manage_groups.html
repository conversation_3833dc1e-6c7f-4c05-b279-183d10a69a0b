{% extends 'base.html' %}

{% block title %}{{ t('User Group Management') }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2>{{ t('User Group Management') }}</h2>
    
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">{{ t('User Groups') }}</h5>
            <button class="btn btn-primary btn-sm" id="createGroupBtn">
                <i class="fas fa-plus"></i> {{ t('Create Group') }}
            </button>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover" id="groupsTable">
                    <thead>
                        <tr>
                            <th>{{ t('Group Name') }}</th>
                            <th>{{ t('Description') }}</th>
                            <th>{{ t('Members') }}</th>
                            <th>{{ t('Permissions') }}</th>
                            <th>{{ t('Storage Limit') }}</th>
                            <th>{{ t('Created By') }}</th>
                            <th>{{ t('Create Date') }}</th>
                            <th>{{ t('Actions') }}</th>
                        </tr>
                    </thead>
                    <tbody id="groupsTableBody">
                        <!-- 组列表将通过JavaScript加载 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- 创建/编辑用户组模态框 -->
<div class="modal fade" id="groupModal" tabindex="-1" aria-labelledby="groupModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="groupModalLabel">{{ t('Create User Group') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="groupForm">
                    <input type="hidden" id="groupId" value="">
                    
                    <div class="mb-3">
                        <label for="groupName" class="form-label">{{ t('Group Name') }} *</label>
                        <input type="text" class="form-control" id="groupName" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="groupDescription" class="form-label">{{ t('Description') }}</label>
                        <textarea class="form-control" id="groupDescription" rows="2"></textarea>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">{{ t('Permissions') }}</div>
                                <div class="card-body">
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="canUpload" checked>
                                        <label class="form-check-label" for="canUpload">
                                            {{ t('Can Upload Files') }}
                                        </label>
                                    </div>
                                    
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="canDownload" checked>
                                        <label class="form-check-label" for="canDownload">
                                            {{ t('Can Download Files') }}
                                        </label>
                                    </div>
                                    
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="canDelete" checked>
                                        <label class="form-check-label" for="canDelete">
                                            {{ t('Can Delete Files') }}
                                        </label>
                                    </div>
                                    
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="canShare" checked>
                                        <label class="form-check-label" for="canShare">
                                            {{ t('Can Share Files') }}
                                        </label>
                                    </div>
                                    
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="isAdminGroup">
                                        <label class="form-check-label" for="isAdminGroup">
                                            {{ t('Administrator Group') }}
                                        </label>
                                        <small class="form-text text-muted d-block">
                                            {{ t('Members will have administrator privileges') }}
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">{{ t('Storage Settings') }}</div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="storageLimit" class="form-label">{{ t('Storage Limit (MB)') }}</label>
                                        <input type="number" class="form-control" id="storageLimit" value="0" min="0">
                                        <small class="form-text text-muted">
                                            {{ t('0 = Unlimited') }}
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="groupMembers" class="form-label">{{ t('Group Members') }}</label>
                        <select class="form-select" id="groupMembers" multiple size="6">
                            {% for user in users %}
                            <option value="{{ user.id }}">{{ user.username }}</option>
                            {% endfor %}
                        </select>
                        <small class="form-text text-muted">
                            {{ t('Hold Ctrl key to select multiple users') }}
                        </small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ t('Cancel') }}</button>
                <button type="button" class="btn btn-primary" id="saveGroupBtn">{{ t('Save') }}</button>
            </div>
        </div>
    </div>
</div>

<!-- 成员管理模态框 -->
<div class="modal fade" id="membersModal" tabindex="-1" aria-labelledby="membersModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="membersModalLabel">{{ t('Manage Group Members') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="currentGroupId">
                
                <div class="mb-3">
                    <label for="availableUsers" class="form-label">{{ t('Available Users') }}</label>
                    <select class="form-select" id="availableUsers" size="5">
                        {% for user in users %}
                        <option value="{{ user.id }}">{{ user.username }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="d-flex justify-content-center my-2">
                    <button class="btn btn-outline-primary btn-sm mx-1" id="addMemberBtn">
                        <i class="fas fa-arrow-down"></i>
                    </button>
                    <button class="btn btn-outline-danger btn-sm mx-1" id="removeMemberBtn">
                        <i class="fas fa-arrow-up"></i>
                    </button>
                </div>
                
                <div class="mb-3">
                    <label for="currentMembers" class="form-label">{{ t('Current Members') }}</label>
                    <select class="form-select" id="currentMembers" size="5">
                        <!-- 当前成员列表将通过JavaScript加载 -->
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ t('Close') }}</button>
                <button type="button" class="btn btn-primary" id="saveMembersBtn">{{ t('Save Changes') }}</button>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteConfirmModalLabel">{{ t('Confirm Delete') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="deleteGroupId">
                <p>{{ t('Are you sure you want to delete this group?') }}</p>
                <p id="deleteGroupName" class="fw-bold"></p>
                <p class="text-danger">{{ t('This action cannot be undone.') }}</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ t('Cancel') }}</button>
                <button type="button" class="btn btn-primary" id="confirmDeleteBtn">{{ t('Delete') }}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 页面加载完成后加载用户组列表
document.addEventListener('DOMContentLoaded', function() {
    loadGroups();
    
    // 绑定创建组按钮的点击事件
    const createBtn = document.getElementById('createGroupBtn');
    createBtn.addEventListener('click', function() {
        showCreateGroupModal();
    });
    
    // 使用事件委托为表格中的按钮绑定事件
    document.getElementById('groupsTableBody').addEventListener('click', function(event) {
        // 查找被点击的按钮
        let target = event.target;
        
        // 如果点击的是图标，找到它的父级按钮
        if (target.tagName === 'I') {
            target = target.parentElement;
        }
        
        // 根据按钮类型执行相应操作
        if (target.classList.contains('show-members-btn')) {
            const groupId = parseInt(target.dataset.groupId);
            showMembersModal(groupId);
        } else if (target.classList.contains('edit-group-btn')) {
            const groupId = parseInt(target.dataset.groupId);
            editGroup(groupId);
        } else if (target.classList.contains('delete-group-btn')) {
            const groupId = parseInt(target.dataset.groupId);
            const groupName = target.dataset.groupName;
            deleteGroup(groupId, groupName);
        }
    });
    
    // 绑定模态框中的按钮事件
    document.getElementById('saveGroupBtn').addEventListener('click', function() {
        saveGroup();
    });
    
    document.getElementById('saveMembersBtn').addEventListener('click', function() {
        saveMembers();
    });
    
    document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
        confirmDelete();
    });
    
    document.getElementById('addMemberBtn').addEventListener('click', function() {
        addMember();
    });
    
    document.getElementById('removeMemberBtn').addEventListener('click', function() {
        removeMember();
    });
});

// 加载用户组列表
function loadGroups() {
    fetch('/api/groups')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const tbody = document.getElementById('groupsTableBody');
                tbody.innerHTML = '';
                
                // 确保 data.groups 存在且是一个数组
                const groups = Array.isArray(data.groups) ? data.groups : [];
                
                if (groups.length === 0) {
                    const tr = document.createElement('tr');
                    tr.innerHTML = `<td colspan="8" class="text-center">{{ t('No groups found') }}</td>`;
                    tbody.appendChild(tr);
                    return;
                }
                
                groups.forEach(group => {
                    const tr = document.createElement('tr');
                    tr.innerHTML = `
                        <td>${group.name || ''}</td>
                        <td>${group.description || ''}</td>
                        <td>${group.member_count || 0}</td>
                        <td>
                            ${group.can_upload ? '{{ t("Can Upload Files") }}' : ''}
                            ${group.can_download ? '{{ t("Can Download Files") }}' : ''}
                            ${group.can_delete ? '{{ t("Can Delete Files") }}' : ''}
                            ${group.can_share ? '{{ t("Can Share Files") }}' : ''}
                        </td>
                        <td>${group.storage_limit === 0 ? '{{ t("Unlimited") }}' : (group.storage_limit || 0) + ' MB'}</td>
                        <td>${group.created_by || ''}</td>
                        <td>${group.create_date || ''}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-outline-primary show-members-btn" data-group-id="${group.id || ''}" title="{{ t('Manage Members') }}">
                                    <i class="fas fa-users"></i>
                                </button>
                                <button type="button" class="btn btn-outline-secondary edit-group-btn" data-group-id="${group.id || ''}" title="{{ t('Edit') }}">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-outline-danger delete-group-btn" data-group-id="${group.id || ''}" data-group-name="${group.name || ''}" title="{{ t('Delete') }}">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    `;
                    tbody.appendChild(tr);
                });
            } else {
                alert(data.message || '{{ t("An error occurred while loading groups") }}');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            const tbody = document.getElementById('groupsTableBody');
            tbody.innerHTML = `<tr><td colspan="8" class="text-center text-danger">{{ t('An error occurred while loading groups') }}</td></tr>`;
        });
}

// 显示创建用户组模态框
function showCreateGroupModal() {
    document.getElementById('groupId').value = '';
    document.getElementById('groupName').value = '';
    document.getElementById('groupDescription').value = '';
    document.getElementById('canUpload').checked = true;
    document.getElementById('canDownload').checked = true;
    document.getElementById('canDelete').checked = true;
    document.getElementById('canShare').checked = true;
    document.getElementById('isAdminGroup').checked = false;
    document.getElementById('storageLimit').value = '0';
    document.getElementById('groupModalLabel').textContent = '{{ t("Create User Group") }}';
    
    const modal = new bootstrap.Modal(document.getElementById('groupModal'));
    modal.show();
}

// 显示编辑用户组模态框
function editGroup(groupId) {
    fetch(`/api/groups/${groupId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const group = data.group;
                document.getElementById('groupId').value = group.id;
                document.getElementById('groupName').value = group.name;
                document.getElementById('groupDescription').value = group.description || '';
                document.getElementById('canUpload').checked = group.can_upload;
                document.getElementById('canDownload').checked = group.can_download;
                document.getElementById('canDelete').checked = group.can_delete;
                document.getElementById('canShare').checked = group.can_share;
                document.getElementById('isAdminGroup').checked = group.is_admin_group;
                document.getElementById('storageLimit').value = group.storage_limit;
                document.getElementById('groupModalLabel').textContent = '{{ t("Edit User Group") }}';
                
                // 获取组成员列表
                fetch(`/api/groups/${groupId}/members`)
                    .then(response => response.json())
                    .then(membersData => {
                        if (membersData.success) {
                            const groupMembers = document.getElementById('groupMembers');
                            const memberIds = membersData.members.map(member => member.id);
                            
                            // 遍历所有可选项，设置选中状态
                            for (let i = 0; i < groupMembers.options.length; i++) {
                                const option = groupMembers.options[i];
                                option.selected = memberIds.includes(parseInt(option.value));
                            }
                        }
                    })
                    .catch(error => {
                        console.error('Error loading members:', error);
                    });
                
                const modal = new bootstrap.Modal(document.getElementById('groupModal'));
                modal.show();
            } else {
                alert(data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('{{ t("An error occurred while loading group data") }}');
        });
}

// 保存用户组
function saveGroup() {
    const groupId = document.getElementById('groupId').value;
    const groupName = document.getElementById('groupName').value;
    const groupDescription = document.getElementById('groupDescription').value;
    const canUpload = document.getElementById('canUpload').checked;
    const canDownload = document.getElementById('canDownload').checked;
    const canDelete = document.getElementById('canDelete').checked;
    const canShare = document.getElementById('canShare').checked;
    const isAdminGroup = document.getElementById('isAdminGroup').checked;
    const storageLimit = parseInt(document.getElementById('storageLimit').value);
    
    // 获取选中的组成员
    const groupMembers = document.getElementById('groupMembers');
    const memberIds = Array.from(groupMembers.selectedOptions).map(option => parseInt(option.value));
    
    if (!groupName) {
        alert('{{ t("Group name cannot be empty") }}');
        return;
    }
    
    const data = {
        name: groupName,
        description: groupDescription,
        can_upload: canUpload,
        can_download: canDownload,
        can_delete: canDelete,
        can_share: canShare,
        is_admin_group: isAdminGroup,
        storage_limit: storageLimit,
        member_ids: memberIds
    };
    
    const url = groupId ? `/api/groups/${groupId}` : '/api/groups';
    const method = groupId ? 'PUT' : 'POST';
    
    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('{{ t("An error occurred while saving the group") }}');
    });
}

// 显示成员管理模态框
function showMembersModal(groupId) {
    document.getElementById('currentGroupId').value = groupId;
    
    fetch(`/api/groups/${groupId}/members`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const currentMembers = document.getElementById('currentMembers');
                currentMembers.innerHTML = '';
                
                data.members.forEach(member => {
                    const option = document.createElement('option');
                    option.value = member.id;
                    option.textContent = member.username;
                    currentMembers.appendChild(option);
                });
                
                const modal = new bootstrap.Modal(document.getElementById('membersModal'));
                modal.show();
            } else {
                alert(data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('{{ t("An error occurred while loading group members") }}');
        });
}

// 添加成员
function addMember() {
    const groupId = document.getElementById('currentGroupId').value;
    const availableUsers = document.getElementById('availableUsers');
    const currentMembers = document.getElementById('currentMembers');
    
    const selectedOptions = Array.from(availableUsers.selectedOptions);
    selectedOptions.forEach(option => {
        const newOption = document.createElement('option');
        newOption.value = option.value;
        newOption.textContent = option.textContent;
        currentMembers.appendChild(newOption);
        option.remove();
    });
}

// 移除成员
function removeMember() {
    const groupId = document.getElementById('currentGroupId').value;
    const availableUsers = document.getElementById('availableUsers');
    const currentMembers = document.getElementById('currentMembers');
    
    // Add null check to ensure currentMembers and availableUsers exist
    if (!currentMembers) {
        console.error('{{ t("Current members list not found") }}');
        return;
    }
    
    if (!availableUsers) {
        console.error('{{ t("Available users list not found") }}');
        return;
    }
    
    // Add null check to ensure selectedOptions exists
    if (!currentMembers.selectedOptions || currentMembers.selectedOptions.length === 0) {
        alert('{{ t("Please select members to remove") }}');
        return;
    }
    
    const selectedOptions = Array.from(currentMembers.selectedOptions);
    selectedOptions.forEach(option => {
        const newOption = document.createElement('option');
        newOption.value = option.value;
        newOption.textContent = option.textContent;
        availableUsers.appendChild(newOption);
        option.remove();
    });
}

// 保存成员更改
function saveMembers() {
    const groupId = document.getElementById('currentGroupId').value;
    if (!groupId) {
        console.error('{{ t("Group ID not found") }}');
        return;
    }

    const currentMembers = document.getElementById('currentMembers');
    if (!currentMembers) {
        console.error('{{ t("Members list element not found") }}');
        return;
    }

    // 确保我们有一个有效的选项数组
    const options = currentMembers.options || [];
    const memberIds = [];
    
    // 使用传统的for循环来避免Array.from可能的问题
    for (let i = 0; i < options.length; i++) {
        if (options[i] && options[i].value) {
            memberIds.push(options[i].value);
        }
    }
    
    console.log('Member IDs to save:', memberIds);
    
    fetch(`/api/groups/${groupId}/members`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ member_ids: memberIds })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('{{ t("An error occurred while updating group members") }}');
    });
}

// 删除用户组
function deleteGroup(groupId, groupName) {
    document.getElementById('deleteGroupId').value = groupId;
    document.getElementById('deleteGroupName').textContent = groupName;
    
    const modal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
    modal.show();
}

// 确认删除
function confirmDelete() {
    const groupId = document.getElementById('deleteGroupId').value;
    
    fetch(`/api/groups/${groupId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('{{ t("An error occurred while deleting the group") }}');
    });
}
</script>
{% endblock %} 