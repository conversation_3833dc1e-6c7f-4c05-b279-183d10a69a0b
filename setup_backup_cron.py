#!/usr/bin/env python3
"""
设置备份系统的定时任务
支持Linux(cron)和Windows(计划任务)
"""
import os
import sys
import platform
import subprocess
import tempfile
import argparse

# 获取脚本所在目录
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))

def setup_linux_cron(backup_time="0 2 * * *", python_path=None):
    """
    在Linux系统上设置cron任务
    默认为每天凌晨2点执行备份
    """
    if not python_path:
        python_path = sys.executable
    
    backup_script = os.path.join(SCRIPT_DIR, "backup_system.py")
    if not os.path.exists(backup_script):
        print(f"错误: 备份脚本不存在: {backup_script}")
        return False
    
    # 创建cron任务内容
    cron_line = f"{backup_time} cd {SCRIPT_DIR} && {python_path} {backup_script} >> {SCRIPT_DIR}/logs/backup_cron.log 2>&1\n"
    
    # 获取当前cron任务
    try:
        current_crontab = subprocess.check_output("crontab -l", shell=True).decode('utf-8')
    except subprocess.CalledProcessError:
        current_crontab = ""
    
    # 检查任务是否已存在
    if backup_script in current_crontab:
        print("备份cron任务已存在，将被更新")
        # 移除现有任务行
        lines = current_crontab.splitlines()
        current_crontab = "\n".join([line for line in lines if backup_script not in line])
        current_crontab += "\n"
    
    # 添加新任务
    new_crontab = current_crontab + cron_line
    
    # 保存新的crontab
    with tempfile.NamedTemporaryFile(mode='w+', delete=False) as temp:
        temp.write(new_crontab)
        temp_path = temp.name
    
    try:
        subprocess.run(f"crontab {temp_path}", shell=True, check=True)
        print(f"成功设置定时备份任务: {cron_line.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"设置cron任务失败: {str(e)}")
        return False
    finally:
        os.unlink(temp_path)  # 删除临时文件

def setup_windows_task(backup_time="02:00", python_path=None):
    """
    在Windows系统上设置计划任务
    默认为每天凌晨2点执行备份
    """
    if not python_path:
        python_path = sys.executable
    
    backup_script = os.path.join(SCRIPT_DIR, "backup_system.py")
    if not os.path.exists(backup_script):
        print(f"错误: 备份脚本不存在: {backup_script}")
        return False
    
    # 任务名称
    task_name = "FileManagerBackup"
    
    # 删除现有任务（如果存在）
    try:
        subprocess.run(f'schtasks /query /tn "{task_name}"', shell=True, check=False)
        # 如果任务存在，删除它
        print(f"删除现有任务: {task_name}")
        subprocess.run(f'schtasks /delete /tn "{task_name}" /f', shell=True, check=False)
    except:
        pass
    
    # 创建新任务
    try:
        cmd = f'schtasks /create /tn "{task_name}" /tr "cmd /c cd /d {SCRIPT_DIR} && {python_path} {backup_script} >> {SCRIPT_DIR}\\logs\\backup_cron.log 2>&1" /sc DAILY /st {backup_time} /ru SYSTEM /f'
        subprocess.run(cmd, shell=True, check=True)
        print(f"成功设置每天 {backup_time} 的备份任务")
        return True
    except subprocess.CalledProcessError as e:
        print(f"设置Windows计划任务失败: {str(e)}")
        return False

def main():
    parser = argparse.ArgumentParser(description='设置文件管理系统的定期备份任务')
    parser.add_argument('--time', type=str, help='备份时间 (Linux格式: "0 2 * * *", Windows格式: "02:00")')
    parser.add_argument('--python', type=str, help='Python解释器路径 (默认: 当前Python)')
    
    args = parser.parse_args()
    
    system = platform.system()
    
    if system == 'Linux':
        backup_time = args.time if args.time else "0 2 * * *"
        print(f"在Linux系统上设置定时任务，时间为: {backup_time}")
        setup_linux_cron(backup_time, args.python)
    elif system == 'Windows':
        backup_time = args.time if args.time else "02:00"
        print(f"在Windows系统上设置计划任务，时间为: {backup_time}")
        setup_windows_task(backup_time, args.python)
    else:
        print(f"不支持的操作系统: {system}")
        print("请手动设置定时备份任务")
        print(f"备份脚本位置: {os.path.join(SCRIPT_DIR, 'backup_system.py')}")
        
if __name__ == "__main__":
    main() 