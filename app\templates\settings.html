{% extends "base.html" %}
{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3>{{ t('user_settings') }}</h3>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <h4>{{ t('language_settings') }}</h4>
                        <div class="form-group">
                            <label for="language" class="form-label">{{ t('select_language') }}</label>
                            <select id="language" class="form-select" onchange="changeLanguage(this.value)">
                                <option value="zh" {% if current_language == 'zh' %}selected{% endif %}>中文</option>
                                <option value="en" {% if current_language == 'en' %}selected{% endif %}>English</option>
                            </select>
                            <div class="form-text">
                                {{ t('page_refresh_note') }}
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('main.index') }}" class="btn btn-secondary">
                            {{ t('back_to_home') }}
                        </a>
                        <a href="{{ url_for('main.change_password') }}" class="btn btn-primary">
                            {{ t('change_password') }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function changeLanguage(language) {
    fetch(`/set_language/${language}`, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 重新加载页面以应用新语言
            window.location.reload();
        } else {
            alert(data.message);
        }
    })
    .catch(error => {
        console.error('更改语言失败:', error);
        alert(language === 'zh' ? '更改语言失败' : 'Failed to change language');
    });
}
</script>
{% endblock %} 