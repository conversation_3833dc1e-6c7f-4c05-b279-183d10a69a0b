from app import create_app, db
from app.models import User
import os

def check_db():
    app = create_app()
    with app.app_context():
        # 检查数据库文件是否存在
        db_path = app.config['SQLALCHEMY_DATABASE_URI'].replace('sqlite:///', '')
        print(f"数据库路径: {db_path}")
        print(f"数据库文件是否存在: {os.path.exists(db_path)}")
        
        # 确保数据库表存在
        db.create_all()
        
        # 检查是否有用户
        users = User.query.all()
        print(f"\n现有用户数量: {len(users)}")
        for user in users:
            print(f"用户名: {user.username}, 是否管理员: {user.is_admin}")
        
        # 如果没有用户，创建默认用户
        if not users:
            print("\n创建默认用户...")
            admin = User(username='cv24051', is_admin=True)
            admin.set_password('admin123')
            db.session.add(admin)
            
            user = User(username='user', is_admin=False)
            user.set_password('user123')
            db.session.add(user)
            
            db.session.commit()
            print("默认用户创建成功")

if __name__ == '__main__':
    check_db() 