#!/bin/bash

# Ubuntu MySQL数据库字段修复脚本
# 使用方法: ./fix_ubuntu_mysql.sh

echo "=== MySQL数据库字段修复脚本 ==="
echo "开始修复UserSignature和File表的缺失字段..."

# 检查MySQL是否运行
if ! systemctl is-active --quiet mysql; then
    echo "❌ MySQL服务未运行，正在启动..."
    sudo systemctl start mysql
    sleep 3
fi

# 数据库连接信息（请根据实际情况修改）
DB_HOST="localhost"
DB_USER="fileman"
DB_PASSWORD="CV24051zhou"
DB_NAME="file_manager"

echo "正在连接到MySQL数据库..."

# 执行SQL修复脚本
mysql -h"$DB_HOST" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" << 'EOF'

-- 修复UserSignature表的signature_id字段
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'user_signature'
    AND COLUMN_NAME = 'signature_id'
);

SELECT CASE 
    WHEN @column_exists = 0 THEN '需要添加signature_id字段'
    ELSE 'signature_id字段已存在'
END as signature_id_status;

-- 添加signature_id字段（如果不存在）
SET @sql = IF(@column_exists = 0,
    'ALTER TABLE user_signature ADD COLUMN signature_id INT NULL',
    'SELECT "UserSignature表已包含signature_id字段" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 修复File表的physical_path字段
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'file'
    AND COLUMN_NAME = 'physical_path'
);

SELECT CASE 
    WHEN @column_exists = 0 THEN '需要添加physical_path字段'
    ELSE 'physical_path字段已存在'
END as physical_path_status;

-- 添加physical_path字段（如果不存在）
SET @sql = IF(@column_exists = 0,
    'ALTER TABLE file ADD COLUMN physical_path VARCHAR(500) NULL',
    'SELECT "File表已包含physical_path字段" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 验证修复结果
SELECT '=== 修复结果验证 ===' as result;

SELECT 
    'user_signature表字段' as info,
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'user_signature'
AND COLUMN_NAME IN ('id', 'user_id', 'signature_id', 'signature_data')
ORDER BY ORDINAL_POSITION;

SELECT 
    'file表字段' as info,
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'file'
AND COLUMN_NAME IN ('id', 'filename', 'path', 'physical_path')
ORDER BY ORDINAL_POSITION;

SELECT '✅ MySQL数据库字段修复完成！' as final_result;

EOF

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ 数据库字段修复成功！"
    echo "请重启您的Flask应用程序以使更改生效。"
    echo ""
    echo "重启命令示例："
    echo "sudo systemctl restart your-app-service"
    echo "或者如果使用gunicorn:"
    echo "pkill -f gunicorn && nohup gunicorn -c gunicorn.conf.py run:app &"
else
    echo ""
    echo "❌ 数据库字段修复失败！"
    echo "请检查数据库连接信息和权限。"
fi
