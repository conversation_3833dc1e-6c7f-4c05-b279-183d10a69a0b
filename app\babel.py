from flask import request, session, g
from flask_babel import Babel

babel = Babel()

def get_locale():
    # 优先使用用户设置中保存的语言
    if 'language' in session:
        return session['language']
        
    # 否则使用浏览器请求头中的语言
    return request.accept_languages.best_match(['zh', 'en'])

def init_babel(app):
    babel.init_app(app, locale_selector=get_locale)
    
    # 添加Jinja2上下文处理器，使语言在所有模板中可访问
    @app.context_processor
    def inject_language():
        return {'current_language': session.get('language', 'zh')} 