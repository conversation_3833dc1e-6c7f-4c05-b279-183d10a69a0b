#!/bin/bash

# 生产环境启动脚本

# 切换到应用目录
cd "$(dirname "$0")"

# 激活虚拟环境
source myenv1/bin/activate

# 设置环境变量
export FLASK_ENV=production
export FLASK_DEBUG=0

# 关闭之前的进程（如果存在）
pkill -f "gunicorn -w 4 -b 0.0.0.0:2026 run:app" || true

# 确保日志目录存在
mkdir -p logs

# 使用nohup在后台运行
nohup gunicorn -w 8 -b 0.0.0.0:2026 run:app > logs/gunicorn.log 2>&1 &

# 获取进程ID并显示状态
PID=$!
echo "启动生产服务，进程ID: $PID"
echo "日志文件: $(pwd)/logs/gunicorn.log"

# 等待几秒以确保服务启动
sleep 3

# 检查是否成功启动
if ps -p $PID > /dev/null; then
    echo "服务已成功启动，可以通过 http://服务器IP:2026 访问"
    echo "使用以下命令检查状态:"
    echo "  ps aux | grep gunicorn"
    echo "使用以下命令查看日志:"
    echo "  tail -f logs/gunicorn.log"
    echo "使用以下命令停止服务:"
    echo "  bash stop_prod.sh"
else
    echo "服务启动失败，请检查logs/gunicorn.log查看详细信息"
fi 