# MySQL数据库字段修复指南

## 问题描述

您的应用程序出现以下错误：
1. `type object 'UserSignature' has no attribute 'signature_id'`
2. `'File' object has no attribute 'physical_path'`

这是因为数据库表中缺少对应的字段。

## 解决方案

### 方案一：使用Python脚本修复（推荐）

1. **上传修复脚本到Ubuntu服务器**
   ```bash
   # 将 fix_ubuntu_fields.py 上传到项目根目录
   scp fix_ubuntu_fields.py user@your-server:/path/to/your/project/
   ```

2. **在Ubuntu服务器上执行修复**
   ```bash
   cd /path/to/your/project
   python3 fix_ubuntu_fields.py
   ```

3. **重启应用程序**
   ```bash
   # 如果使用systemd服务
   sudo systemctl restart your-app-service
   
   # 如果使用gunicorn
   pkill -f gunicorn
   nohup gunicorn -c gunicorn.conf.py run:app &
   ```

### 方案二：直接执行SQL语句

1. **连接到MySQL数据库**
   ```bash
   mysql -u fileman -p file_manager
   ```

2. **执行以下SQL语句**
   ```sql
   -- 添加UserSignature表的signature_id字段
   ALTER TABLE user_signature ADD COLUMN signature_id INT NULL;
   
   -- 添加File表的physical_path字段
   ALTER TABLE file ADD COLUMN physical_path VARCHAR(500) NULL;
   
   -- 验证字段是否添加成功
   DESCRIBE user_signature;
   DESCRIBE file;
   ```

3. **重启应用程序**

### 方案三：使用Shell脚本修复

1. **上传并执行Shell脚本**
   ```bash
   # 上传脚本
   scp fix_ubuntu_mysql.sh user@your-server:/path/to/your/project/
   
   # 添加执行权限
   chmod +x fix_ubuntu_mysql.sh
   
   # 执行脚本
   ./fix_ubuntu_mysql.sh
   ```

## 验证修复结果

修复完成后，可以通过以下SQL语句验证：

```sql
-- 检查UserSignature表字段
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'file_manager' 
AND TABLE_NAME = 'user_signature';

-- 检查File表字段
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'file_manager' 
AND TABLE_NAME = 'file';
```

## 注意事项

1. **备份数据库**：在执行修复前，建议备份数据库
   ```bash
   mysqldump -u fileman -p file_manager > backup_$(date +%Y%m%d_%H%M%S).sql
   ```

2. **检查权限**：确保数据库用户有ALTER TABLE权限

3. **停止应用**：修复期间建议停止应用程序以避免冲突

4. **测试功能**：修复后测试PDF签名功能是否正常工作

## 常见问题

### Q: 如果出现"Access denied"错误怎么办？
A: 检查数据库用户权限，可能需要GRANT ALTER权限：
```sql
GRANT ALTER ON file_manager.* TO 'fileman'@'localhost';
FLUSH PRIVILEGES;
```

### Q: 如果字段已存在会怎样？
A: 脚本会检查字段是否存在，如果已存在则跳过，不会重复添加。

### Q: 修复后还是有错误怎么办？
A: 
1. 检查应用程序是否已重启
2. 检查模型定义是否正确
3. 查看应用程序日志获取详细错误信息

## 联系支持

如果遇到问题，请提供：
1. 错误日志
2. 数据库表结构（DESCRIBE table_name）
3. 应用程序配置信息
