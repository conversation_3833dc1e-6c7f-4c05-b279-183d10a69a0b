"""添加用户组和权限

Revision ID: add_user_groups_and_permissions
Revises: update_keywords_table
Create Date: 2025-04-09 09:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_user_groups_and_permissions'
down_revision = 'update_keywords_table'
branch_labels = None
depends_on = None


def upgrade():
    # 创建用户组表
    op.create_table(
        'user_group',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('description', sa.String(length=255), nullable=True),
        sa.Column('created_by', sa.Integer(), nullable=False),
        sa.Column('create_date', sa.DateTime(), nullable=True),
        sa.Column('can_upload', sa.Boolean(), nullable=True, server_default='1'),
        sa.Column('can_download', sa.<PERSON>(), nullable=True, server_default='1'),
        sa.<PERSON>umn('can_delete', sa.<PERSON>(), nullable=True, server_default='1'),
        sa.Column('can_share', sa.Boolean(), nullable=True, server_default='1'),
        sa.Column('storage_limit', sa.Integer(), nullable=True, server_default='0'),
        sa.Column('is_admin_group', sa.Boolean(), nullable=True, server_default='0'),
        sa.ForeignKeyConstraint(['created_by'], ['user.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # 创建用户-组关联表
    op.create_table(
        'user_group_members',
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('group_id', sa.Integer(), nullable=False),
        sa.Column('join_date', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['group_id'], ['user_group.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['user_id'], ['user.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('user_id', 'group_id')
    )
    
    # 在文件夹表中添加用户组权限字段
    op.add_column('folder', sa.Column('allowed_groups', sa.String(length=500), nullable=True, server_default=''))


def downgrade():
    # 删除文件夹表中的用户组权限字段
    op.drop_column('folder', 'allowed_groups')
    
    # 删除用户-组关联表
    op.drop_table('user_group_members')
    
    # 删除用户组表
    op.drop_table('user_group') 