<!DOCTYPE html>
<html lang="{{ session.get('language', 'zh') }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ t('File Management System') }}</title>
    <link href="{{ url_for('static', filename='css/bootstrap.min.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/all.min.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/font-fix.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/language-flags.css') }}" rel="stylesheet">
    <style>
        .wrapper {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 250px;
            background-color: #212529;
            color: #fff;
            padding: 20px;
            transition: all 0.3s ease;
        }
        
        .main-content {
            flex: 1;
            padding: 20px;
            background-color: #f8f9fa;
        }
        
        .top-bar {
            display: flex;
            justify-content: flex-end;
            padding: 10px 20px;
            background-color: #fff;
            border-bottom: 1px solid #dee2e6;
            margin: -20px -20px 20px -20px;
        }
        
        .system-title {
            font-size: 1.5rem;
            color: #fff;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid rgba(255,255,255,.1);
        }
        
        .upload-form {
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid rgba(255,255,255,.1);
        }
        
        .table {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,.05);
        }
        
        .alert {
            margin-bottom: 1rem;
            border-radius: 4px;
        }
        
        .dropdown-menu {
            min-width: 200px;
            padding: 0.5rem 0;
            margin: 0;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,.1);
        }
        
        .dropdown-item {
            padding: 0.5rem 1rem;
        }
        
        .dropdown-divider {
            margin: 0.5rem 0;
        }
        
        @media (max-width: 768px) {
            .wrapper {
                flex-direction: column;
            }
            .sidebar {
                width: 100%;
            }
        }
        
        .upload-area {
            border: 2px dashed rgba(255,255,255,.2);
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .upload-area:hover {
            border-color: rgba(255,255,255,.4);
        }
        
        .upload-area.dragover {
            border-color: rgba(255,255,255,.6);
            background-color: rgba(255,255,255,.1);
        }
        
        .file-list {
            text-align: left;
            margin-top: 10px;
        }
        
        .selected-files {
            max-height: 150px;
            overflow-y: auto;
            font-size: 0.875rem;
        }
        
        .selected-files div {
            padding: 2px 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="system-title">{{ t('File Management System') }}</div>
            
            {% if current_user.is_authenticated and request.endpoint == 'main.index' %}
            <div class="upload-form">
                <h5 class="mb-3">{{ t('Upload Files') }}</h5>
                <form action="{{ url_for('main.upload_file') }}" method="POST" enctype="multipart/form-data" id="uploadForm">
                    <input type="hidden" name="folder_id" value="{{ current_folder.id if current_folder else '' }}">
                    <div class="mb-3">
                        <div class="btn-group w-100" role="group">
                            <input type="radio" class="btn-check" name="file_type" id="fileType1" value="file" checked>
                            <label class="btn btn-outline-light btn-sm" for="fileType1">{{ t('File Upload') }}</label>
                            
                            <input type="radio" class="btn-check" name="file_type" id="fileType2" value="folder">
                            <label class="btn btn-outline-light btn-sm" for="fileType2">{{ t('Folder Upload') }}</label>
                        </div>
                    </div>
                    
                    <div class="upload-container mb-3">
                        <!-- 文件上传区域 -->
                        <div id="fileUploadArea" class="upload-area">
                            <input type="file" class="file-input" name="file" id="fileInput" multiple style="display: none;">
                            <div class="upload-placeholder" onclick="document.getElementById('fileInput').click()">
                                <i class="bi bi-cloud-arrow-up fs-3"></i>
                                <p class="mb-0 mt-2">{{ t('Click to select files') }}</p>
                                <small class="text-muted">{{ t('Support multiple file uploads') }}</small>
                            </div>
                            <div id="fileList" class="file-list" style="display: none;">
                                <div class="selected-files mb-2"></div>
                                <button type="button" class="btn btn-link btn-sm text-light p-0" onclick="clearFiles()">{{ t('Clear selection') }}</button>
                            </div>
                        </div>
                        
                        <!-- 文件夹上传区域 -->
                        <div id="folderUploadArea" class="upload-area" style="display: none;">
                            <input type="file" class="file-input" name="folder" id="folderInput" 
                                   webkitdirectory directory multiple style="display: none;">
                            <div class="upload-placeholder" onclick="document.getElementById('folderInput').click()">
                                <i class="fas fa-folder-plus fs-3"></i>
                                <p class="mb-0 mt-2">{{ t('Click to select folder') }}</p>
                                <small class="text-muted">{{ t('Folder structure will be preserved') }}</small>
                            </div>
                            <div id="folderList" class="file-list" style="display: none;">
                                <div class="selected-files mb-2"></div>
                                <button type="button" class="btn btn-link btn-sm text-light p-0" onclick="clearFolder()">{{ t('Clear selection') }}</button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">{{ t('Tags') }}</label>
                        <input type="text" class="form-control form-control-sm" name="tags" placeholder="{{ t('Separate with commas') }}">
                    </div>
                    
                    <div class="progress mb-2" style="display: none;">
                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                    <div class="text-center mb-2" style="display: none;" id="progressPercentText">0%</div>
                    
                    <button type="submit" class="btn btn-light btn-sm w-100" id="uploadButton" disabled>{{ t('Upload') }}</button>
                </form>
            </div>
            {% endif %}
        </div>

        <!-- 主要内容区 -->
        <div class="main-content">
            <!-- 顶部栏 -->
            {% if current_user.is_authenticated %}
            <div class="top-bar">
                <!-- 语言切换 -->
                <div class="language-selector">
                    <a href="#" class="language-button {% if current_language() == 'zh' %}active{% endif %}" 
                       onclick="switchLanguage('zh'); return false;">
                        <span class="flag-icon flag-icon-zh"></span>
                        中文
                    </a>
                    <a href="#" class="language-button {% if current_language() == 'en' %}active{% endif %}" 
                       onclick="switchLanguage('en'); return false;">
                        <span class="flag-icon flag-icon-en"></span>
                        English
                    </a>
                </div>

                <div class="dropdown">
                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="settingsDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        {{ current_user.username }}
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="settingsDropdown">
                        <li><a class="dropdown-item" href="{{ url_for('main.index') }}">
                            <i class="fas fa-home"></i> {{ t('Home') }}
                        </a></li>
                        <li><div class="dropdown-divider"></div></li>
                        {% if current_user.is_super_admin or current_user.is_admin %}
                        <li><a class="dropdown-item" href="{{ url_for('main.admin_users') }}">{{ t('User Management') }}</a></li>
                        <li><a class="dropdown-item" href="{{ url_for('main.view_logs') }}">{{ t('System Logs') }}</a></li>
                        <li><a class="dropdown-item" href="{{ url_for('main.manage_keywords') }}">{{ t('Keyword Management') }}</a></li>
                        <li><a class="dropdown-item" href="{{ url_for('main.manage_groups') }}">{{ t('User Group Management') }}</a></li>
                        {% endif %}
                        {% if current_user.is_super_admin %}
                        <li><a class="dropdown-item" href="{{ url_for('main.trash') }}">
                            <i class="fas fa-trash"></i> {{ t('Trash') }}
                            <span class="badge bg-danger rounded-pill" id="trashCount" style="display: none;"></span>
                        </a></li>
                        {% endif %}
                        <li><div class="dropdown-divider"></div></li>
                        <li><a class="dropdown-item" href="{{ url_for('main.change_password') }}">{{ t('Change Password') }}</a></li>
                        <li><a class="dropdown-item" href="{{ url_for('main.user_signatures') }}">
                            <i class="fas fa-signature"></i> {{ t('My Signatures') }}
                        </a></li>
                        <li><div class="dropdown-divider"></div></li>
                        <li><a class="dropdown-item text-danger" href="{{ url_for('auth.logout') }}">{{ t('Logout') }}</a></li>
                    </ul>
                </div>
            </div>
            {% endif %}

            {% with messages = get_flashed_messages() %}
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-info">{{ message }}</div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            {% block content %}{% endblock %}
        </div>
    </div>
    <script src="{{ url_for('static', filename='js/bootstrap.bundle.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/language-switch.js') }}"></script>
    
    <!-- 确保下拉菜单正常工作 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化所有下拉菜单
            var dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
            dropdownElementList.map(function (dropdownToggleEl) {
                return new bootstrap.Dropdown(dropdownToggleEl);
            });
        });
    </script>
    
    {% if current_user.is_authenticated and request.endpoint == 'main.index' %}
    <script>
    document.querySelectorAll('input[name="file_type"]').forEach(radio => {
        radio.addEventListener('change', function() {
            const fileArea = document.getElementById('fileUploadArea');
            const folderArea = document.getElementById('folderUploadArea');
            
            if (this.value === 'file') {
                fileArea.style.display = 'block';
                folderArea.style.display = 'none';
            } else {
                fileArea.style.display = 'none';
                folderArea.style.display = 'block';
            }
            
            // 清除已选择的文件
            clearFiles();
            clearFolder();
        });
    });

    // 文件拖放处理
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        document.querySelectorAll('.upload-area').forEach(area => {
            area.addEventListener(eventName, preventDefaults, false);
        });
    });

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    document.querySelectorAll('.upload-area').forEach(area => {
        area.addEventListener('dragenter', function(e) {
            this.classList.add('dragover');
        });
        
        area.addEventListener('dragleave', function(e) {
            this.classList.remove('dragover');
        });
        
        area.addEventListener('drop', function(e) {
            this.classList.remove('dragover');
            const input = this.querySelector('input[type="file"]');
            if (input.hasAttribute('webkitdirectory')) {
                // 文件夹上传暂不支持拖放
                alert("{{ t('Please click to select folders for upload') }}");
            } else {
                handleFiles(e.dataTransfer.files);
            }
        });
    });

    // 处理文件选择
    document.getElementById('fileInput').addEventListener('change', function(e) {
        handleFiles(this.files);
    });

    document.getElementById('folderInput').addEventListener('change', function(e) {
        handleFolder(this.files);
    });

    function handleFiles(files) {
        const fileList = document.getElementById('fileList');
        const selectedFiles = fileList.querySelector('.selected-files');
        const uploadButton = document.getElementById('uploadButton');
        
        if (files.length > 0) {
            fileList.style.display = 'block';
            selectedFiles.innerHTML = '';
            
            const maxDisplay = 10;
            for (let i = 0; i < Math.min(files.length, maxDisplay); i++) {
                selectedFiles.innerHTML += `<div>${files[i].name}</div>`;
            }
            if (files.length > maxDisplay) {
                selectedFiles.innerHTML += `<div>...and ${files.length - maxDisplay} more files</div>`;
            }
            
            uploadButton.disabled = false;
        }
    }

    function handleFolder(files) {
        const folderList = document.getElementById('folderList');
        const selectedFiles = folderList.querySelector('.selected-files');
        const uploadButton = document.getElementById('uploadButton');
        
        if (files.length > 0) {
            // 获取文件夹名称
            const folderPath = files[0].webkitRelativePath;
            const folderName = folderPath.split('/')[0];
            
            folderList.style.display = 'block';
            selectedFiles.innerHTML = `
                <div>{{ t('Folder') }}: ${folderName}</div>
                <div class="text-muted">{{ t('Contains') }} ${files.length} {{ t('files') }}</div>
            `;
            uploadButton.disabled = false;
        }
    }

    function clearFiles() {
        const fileInput = document.getElementById('fileInput');
        const fileList = document.getElementById('fileList');
        const uploadButton = document.getElementById('uploadButton');
        
        fileInput.value = '';
        fileList.style.display = 'none';
        uploadButton.disabled = true;
    }

    function clearFolder() {
        const folderInput = document.getElementById('folderInput');
        const folderList = document.getElementById('folderList');
        const uploadButton = document.getElementById('uploadButton');
        
        folderInput.value = '';
        folderList.style.display = 'none';
        uploadButton.disabled = true;
    }

    // 表单提交处理
    document.getElementById('uploadForm').addEventListener('submit', function(e) {
        e.preventDefault();
        const formData = new FormData(this);
        const progressBar = document.querySelector('.progress');
        const progressBarInner = progressBar.querySelector('.progress-bar');
        const progressText = document.getElementById('progressPercentText');
        const uploadButton = document.getElementById('uploadButton');
        
        progressBar.style.display = 'block';
        progressText.style.display = 'block';
        uploadButton.disabled = true;
        
        fetch(this.action, {
            method: 'POST',
            body: formData
        }).then(response => {
            if (response.ok) {
                window.location.reload();
            } else {
                throw new Error('Upload failed');
            }
        }).catch(error => {
            alert(error.message);
            progressBar.style.display = 'none';
            progressText.style.display = 'none';
            uploadButton.disabled = false;
        });
        
        let progress = 0;
        const interval = setInterval(() => {
            progress += 5;
            if (progress > 90) clearInterval(interval);
            progressBarInner.style.width = progress + '%';
            progressBarInner.setAttribute('aria-valuenow', progress);
            progressText.textContent = progress + '%';
        }, 200);
    });
    </script>
    {% endif %}
    
    {% block scripts %}{% endblock %}
</body>
</html> 