2025-04-10 17:08:07,641 - INFO - 开始系统备份...
2025-04-10 17:08:07,641 - INFO - 创建备份目录: backups/20250410_170807
2025-04-10 17:08:07,642 - INFO - 数据库备份完成: backups/20250410_170807/file_manager.db
2025-04-10 17:08:07,642 - INFO - 配置文件备份完成: app/config/config.py
2025-04-10 17:08:07,642 - INFO - 配置文件备份完成: instance/config.py
2025-04-10 17:08:07,642 - INFO - 配置文件备份完成: requirements.txt
2025-04-10 17:08:07,642 - INFO - 配置文件备份完成: uwsgi.ini
2025-04-10 17:08:07,642 - INFO - 配置文件备份完成: start_prod.sh
2025-04-10 17:08:07,642 - INFO - 配置文件备份完成: stop_prod.sh
2025-04-10 17:08:07,643 - INFO - 备份压缩包创建成功: backups/20250410_170807_backup.zip
2025-04-10 17:08:07,643 - INFO - 系统备份完成
2025-04-14 02:00:01,880 - INFO - 开始系统备份...
2025-04-14 02:00:01,880 - INFO - 创建备份目录: backups/20250414_020001
2025-04-14 02:00:01,881 - INFO - 数据库备份完成: backups/20250414_020001/file_manager.db
2025-04-14 02:00:01,881 - INFO - 配置文件备份完成: app/config/config.py
2025-04-14 02:00:01,881 - INFO - 配置文件备份完成: instance/config.py
2025-04-14 02:00:01,881 - INFO - 配置文件备份完成: requirements.txt
2025-04-14 02:00:01,881 - INFO - 配置文件备份完成: uwsgi.ini
2025-04-14 02:00:01,881 - INFO - 配置文件备份完成: start_prod.sh
2025-04-14 02:00:01,881 - INFO - 配置文件备份完成: stop_prod.sh
2025-04-14 02:00:01,882 - INFO - 备份压缩包创建成功: backups/20250414_020001_backup.zip
2025-04-14 02:00:01,882 - INFO - 系统备份完成
2025-04-21 02:00:01,479 - INFO - 开始系统备份...
2025-04-21 02:00:01,480 - INFO - 创建备份目录: backups/20250421_020001
2025-04-21 02:00:01,481 - INFO - 数据库备份完成: backups/20250421_020001/file_manager.db
2025-04-21 02:00:01,481 - INFO - 配置文件备份完成: app/config/config.py
2025-04-21 02:00:01,481 - INFO - 配置文件备份完成: instance/config.py
2025-04-21 02:00:01,481 - INFO - 配置文件备份完成: requirements.txt
2025-04-21 02:00:01,481 - INFO - 配置文件备份完成: uwsgi.ini
2025-04-21 02:00:01,481 - INFO - 配置文件备份完成: start_prod.sh
2025-04-21 02:00:01,481 - INFO - 配置文件备份完成: stop_prod.sh
2025-04-21 02:00:01,482 - INFO - 备份压缩包创建成功: backups/20250421_020001_backup.zip
2025-04-21 02:00:01,482 - INFO - 系统备份完成
2025-04-28 02:00:01,225 - INFO - 开始系统备份...
2025-04-28 02:00:01,225 - INFO - 创建备份目录: backups/20250428_020001
2025-04-28 02:00:01,226 - INFO - 数据库备份完成: backups/20250428_020001/file_manager.db
2025-04-28 02:00:01,226 - INFO - 配置文件备份完成: app/config/config.py
2025-04-28 02:00:01,226 - INFO - 配置文件备份完成: instance/config.py
2025-04-28 02:00:01,226 - INFO - 配置文件备份完成: requirements.txt
2025-04-28 02:00:01,226 - INFO - 配置文件备份完成: uwsgi.ini
2025-04-28 02:00:01,226 - INFO - 配置文件备份完成: start_prod.sh
2025-04-28 02:00:01,227 - INFO - 配置文件备份完成: stop_prod.sh
2025-04-28 02:00:01,228 - INFO - 备份压缩包创建成功: backups/20250428_020001_backup.zip
2025-04-28 02:00:01,228 - INFO - 系统备份完成
