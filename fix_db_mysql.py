#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
MySQL数据库模型修复脚本
- 为UserSignature添加signature_id字段
- 为File添加physical_path字段
- 为Signature添加file_id字段
"""

import os
import sys
import logging
import pymysql
from configparser import ConfigParser
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 默认配置
DEFAULT_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '',
    'database': 'paperless',
    'port': 3306
}

def read_config():
    """读取数据库配置"""
    config = DEFAULT_CONFIG.copy()
    
    config_path = 'config.ini'
    if os.path.exists(config_path):
        try:
            parser = ConfigParser()
            parser.read(config_path)
            
            if 'database' in parser:
                for key in config.keys():
                    if key in parser['database']:
                        config[key] = parser['database'][key]
                        
            logger.info("已从配置文件读取数据库设置")
        except Exception as e:
            logger.warning(f"读取配置文件失败: {str(e)}")
    else:
        logger.warning("未找到配置文件，使用默认设置")
    
    return config

def create_connection(config):
    """创建数据库连接"""
    try:
        connection = pymysql.connect(
            host=config['host'],
            user=config['user'],
            password=config['password'],
            database=config['database'],
            port=int(config['port']) if 'port' in config else 3306,
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor
        )
        logger.info("数据库连接成功")
        return connection
    except Exception as e:
        logger.error(f"连接数据库失败: {str(e)}")
        return None

def check_table_columns(connection, table_name):
    """检查表的列信息"""
    try:
        with connection.cursor() as cursor:
            cursor.execute(f"SHOW COLUMNS FROM {table_name}")
            columns = cursor.fetchall()
            return [column['Field'] for column in columns]
    except Exception as e:
        logger.error(f"检查表 {table_name} 列信息时出错: {str(e)}")
        return []

def check_table_exists(connection, table_name):
    """检查表是否存在"""
    try:
        with connection.cursor() as cursor:
            cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
            return cursor.fetchone() is not None
    except Exception as e:
        logger.error(f"检查表 {table_name} 是否存在时出错: {str(e)}")
        return False

def fix_user_signature_model(connection):
    """修复UserSignature模型，添加缺失的signature_id字段"""
    logger.info("开始修复UserSignature模型...")
    
    table_name = 'user_signature'
    
    if not check_table_exists(connection, table_name):
        logger.info(f"表 {table_name} 不存在，正在创建...")
        try:
            with connection.cursor() as cursor:
                cursor.execute(f"""
                CREATE TABLE {table_name} (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    signature_id INT,
                    FOREIGN KEY (user_id) REFERENCES user(id),
                    FOREIGN KEY (signature_id) REFERENCES signature(id)
                )
                """)
                connection.commit()
                logger.info(f"表 {table_name} 创建成功")
                return True
        except Exception as e:
            logger.error(f"创建表 {table_name} 时出错: {str(e)}")
            return False
    
    columns = check_table_columns(connection, table_name)
    
    if 'signature_id' not in columns:
        logger.info(f"表 {table_name} 缺少signature_id字段，正在添加...")
        
        try:
            with connection.cursor() as cursor:
                # 添加signature_id列
                cursor.execute(f"""
                ALTER TABLE {table_name} ADD COLUMN signature_id INT;
                """)
                
                # 添加外键约束
                try:
                    if check_table_exists(connection, 'signature'):
                        cursor.execute(f"""
                        ALTER TABLE {table_name} 
                        ADD CONSTRAINT fk_user_signature_signature 
                        FOREIGN KEY (signature_id) REFERENCES signature(id);
                        """)
                        logger.info("已添加signature_id外键约束")
                except Exception as e:
                    logger.warning(f"添加外键约束失败，可能已存在: {str(e)}")
                
                connection.commit()
                logger.info(f"表 {table_name} 修复完成")
                return True
        except Exception as e:
            connection.rollback()
            logger.error(f"修复表 {table_name} 时出错: {str(e)}")
            return False
    else:
        logger.info(f"表 {table_name} 已包含signature_id字段，无需修复")
        return False

def fix_file_model(connection):
    """修复File模型，添加缺失的physical_path字段"""
    logger.info("开始修复File模型...")
    
    table_name = 'file'
    
    if not check_table_exists(connection, table_name):
        logger.error(f"表 {table_name} 不存在，无法修复")
        return False
    
    columns = check_table_columns(connection, table_name)
    
    if 'physical_path' not in columns:
        logger.info(f"表 {table_name} 缺少physical_path字段，正在添加...")
        
        try:
            with connection.cursor() as cursor:
                cursor.execute(f"""
                ALTER TABLE {table_name} ADD COLUMN physical_path VARCHAR(255);
                """)
                connection.commit()
                logger.info(f"表 {table_name} 修复完成")
                return True
        except Exception as e:
            connection.rollback()
            logger.error(f"修复表 {table_name} 时出错: {str(e)}")
            return False
    else:
        logger.info(f"表 {table_name} 已包含physical_path字段，无需修复")
        return False

def check_signature_table(connection):
    """检查signature表是否存在，如果不存在则创建"""
    logger.info("检查signature表...")
    
    table_name = 'signature'
    
    if not check_table_exists(connection, table_name):
        logger.info(f"表 {table_name} 不存在，正在创建...")
        try:
            with connection.cursor() as cursor:
                cursor.execute(f"""
                CREATE TABLE {table_name} (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    file_id INT,
                    signature_date DATETIME,
                    signature_type VARCHAR(20),
                    signature_position TEXT,
                    signature_data LONGTEXT,
                    FOREIGN KEY (file_id) REFERENCES file(id)
                )
                """)
                connection.commit()
                logger.info(f"表 {table_name} 创建成功")
                return True
        except Exception as e:
            connection.rollback()
            logger.error(f"创建表 {table_name} 时出错: {str(e)}")
            return False
    
    columns = check_table_columns(connection, table_name)
    
    if 'file_id' not in columns:
        logger.info(f"表 {table_name} 缺少file_id字段，正在添加...")
        
        try:
            with connection.cursor() as cursor:
                cursor.execute(f"""
                ALTER TABLE {table_name} ADD COLUMN file_id INT;
                """)
                
                # 添加外键约束
                try:
                    cursor.execute(f"""
                    ALTER TABLE {table_name} 
                    ADD CONSTRAINT fk_signature_file 
                    FOREIGN KEY (file_id) REFERENCES file(id);
                    """)
                    logger.info("已添加file_id外键约束")
                except Exception as e:
                    logger.warning(f"添加外键约束失败，可能已存在: {str(e)}")
                
                connection.commit()
                logger.info(f"表 {table_name} 的file_id字段添加成功")
                return True
        except Exception as e:
            connection.rollback()
            logger.error(f"修复表 {table_name} 时出错: {str(e)}")
            return False
    else:
        logger.info(f"表 {table_name} 的file_id字段已存在")
        return False

def update_signature_data(connection):
    """更新已有的签名数据，关联file_id和user_signature表"""
    logger.info("开始更新签名数据关联...")
    
    if not check_table_exists(connection, 'signature') or not check_table_exists(connection, 'user_signature'):
        logger.warning("signature或user_signature表不存在，无法更新数据")
        return False
    
    try:
        with connection.cursor() as cursor:
            # 检查是否有缺少file_id的签名记录
            cursor.execute("""
            SELECT COUNT(*) as count FROM signature WHERE file_id IS NULL
            """)
            result = cursor.fetchone()
            missing_count = result['count'] if result else 0
            
            if missing_count > 0:
                logger.info(f"发现{missing_count}条签名记录缺少file_id值")
                
                # 尝试使用user_signature表关联文件
                cursor.execute("""
                SELECT s.id AS signature_id, us.user_id 
                FROM signature s
                JOIN user_signature us ON s.id = us.signature_id
                WHERE s.file_id IS NULL
                """)
                signatures_to_update = cursor.fetchall()
                
                updated_count = 0
                for sig in signatures_to_update:
                    # 查找该用户最近的文件作为默认值
                    cursor.execute("""
                    SELECT id FROM file 
                    WHERE user_id = %s 
                    ORDER BY id DESC LIMIT 1
                    """, (sig['user_id'],))
                    recent_file = cursor.fetchone()
                    
                    if recent_file:
                        cursor.execute("""
                        UPDATE signature SET file_id = %s WHERE id = %s
                        """, (recent_file['id'], sig['signature_id']))
                        updated_count += 1
                
                if updated_count > 0:
                    connection.commit()
                    logger.info(f"成功更新了{updated_count}条签名记录的file_id")
            else:
                logger.info("所有签名记录都已有file_id值，无需更新")
                
            # 检查是否有缺少signature_id的user_signature记录
            if 'signature_id' in check_table_columns(connection, 'user_signature'):
                cursor.execute("""
                SELECT COUNT(*) as count FROM user_signature WHERE signature_id IS NULL
                """)
                result = cursor.fetchone()
                missing_sig_id_count = result['count'] if result else 0
                
                if missing_sig_id_count > 0:
                    logger.info(f"发现{missing_sig_id_count}条user_signature记录缺少signature_id值")
                    # 可以在这里添加逻辑来更新这些记录
            
            return True
    except Exception as e:
        connection.rollback()
        logger.error(f"更新签名数据时出错: {str(e)}")
        return False

def main():
    """主函数"""
    print("===== MySQL数据库模型修复工具 =====")
    
    # 读取配置
    config = read_config()
    
    # 询问用户配置
    print("\n请确认数据库配置:")
    for key, value in config.items():
        if key == 'password':
            new_value = input(f"  {key} [{'*'*len(str(value))}]: ")
        else:
            new_value = input(f"  {key} [{value}]: ")
        
        if new_value:
            config[key] = new_value
    
    # 创建连接
    connection = create_connection(config)
    if not connection:
        return False
    
    try:
        # 执行修复
        check_signature_table(connection)
        fix_user_signature_model(connection)
        fix_file_model(connection)
        update_signature_data(connection)
        
        print("\n===== 修复完成 =====")
        print("请重启Web应用以应用更改")
        return True
    except Exception as e:
        logger.error(f"修复过程中发生错误: {str(e)}")
        return False
    finally:
        if connection:
            connection.close()
            logger.info("数据库连接已关闭")

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 