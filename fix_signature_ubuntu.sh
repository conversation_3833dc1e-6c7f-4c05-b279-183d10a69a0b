#!/bin/bash

echo "开始修复PDF手写签名功能..."

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3，请先安装"
    echo "运行: sudo apt-get install -y python3"
    exit 1
fi

# 赋予脚本执行权限
chmod +x fix_signature_ui.py

# 运行修复脚本
echo "运行修复脚本..."
python3 fix_signature_ui.py

if [ $? -ne 0 ]; then
    echo "修复失败，请查看上方错误信息"
    exit 1
else
    echo "修复成功完成！"
fi

echo ""
echo "请重启Web应用以应用更改"
echo "如果使用systemd: sudo systemctl restart your-app-service"
echo "如果使用supervisor: sudo supervisorctl restart your-app-process" 