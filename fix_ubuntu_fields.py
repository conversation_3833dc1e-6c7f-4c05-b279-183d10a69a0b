#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Ubuntu环境下修复MySQL数据库缺失字段
- 为UserSignature添加signature_id字段
- 为File添加physical_path字段

使用方法:
1. 上传此文件到Ubuntu服务器的项目根目录
2. 运行: python3 fix_ubuntu_fields.py
"""

import os
import sys
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_database_fields():
    """修复数据库字段"""
    try:
        # 设置环境变量（如果需要）
        os.environ.setdefault('FLASK_APP', 'run.py')
        
        # 导入应用程序
        sys.path.insert(0, os.getcwd())
        from app import create_app, db
        from sqlalchemy import inspect, text
        
        app = create_app()
        
        with app.app_context():
            logger.info("开始修复MySQL数据库缺失字段...")
            
            # 获取数据库检查器
            inspector = inspect(db.engine)
            
            # 检查数据库连接
            try:
                with db.engine.connect() as conn:
                    result = conn.execute(text("SELECT 1"))
                    logger.info("数据库连接成功")
            except Exception as e:
                logger.error(f"数据库连接失败: {str(e)}")
                return False
            
            # 检查表是否存在
            tables = inspector.get_table_names()
            logger.info(f"数据库中的表: {tables}")
            
            success = True
            
            # 修复UserSignature表
            if 'user_signature' in tables:
                logger.info("检查UserSignature表...")
                try:
                    user_signature_columns = [column['name'] for column in inspector.get_columns('user_signature')]
                    logger.info(f"UserSignature表当前字段: {user_signature_columns}")
                    
                    if 'signature_id' not in user_signature_columns:
                        logger.info("UserSignature表缺少signature_id字段，正在添加...")
                        
                        with db.engine.connect() as conn:
                            # 添加signature_id列
                            conn.execute(text("ALTER TABLE user_signature ADD COLUMN signature_id INT NULL"))
                            logger.info("signature_id字段添加成功")
                            
                            # 如果signature表存在，尝试添加外键约束
                            if 'signature' in tables:
                                try:
                                    conn.execute(text("""
                                        ALTER TABLE user_signature 
                                        ADD CONSTRAINT fk_user_signature_signature 
                                        FOREIGN KEY (signature_id) REFERENCES signature (id)
                                        ON DELETE SET NULL ON UPDATE CASCADE
                                    """))
                                    logger.info("外键约束添加成功")
                                except Exception as e:
                                    logger.warning(f"添加外键约束失败（可能已存在）: {str(e)}")
                            
                            conn.commit()
                    else:
                        logger.info("UserSignature表已包含signature_id字段")
                        
                except Exception as e:
                    logger.error(f"修复UserSignature表失败: {str(e)}")
                    success = False
            else:
                logger.warning("UserSignature表不存在")
            
            # 修复File表
            if 'file' in tables:
                logger.info("检查File表...")
                try:
                    file_columns = [column['name'] for column in inspector.get_columns('file')]
                    logger.info(f"File表当前字段: {file_columns}")
                    
                    if 'physical_path' not in file_columns:
                        logger.info("File表缺少physical_path字段，正在添加...")
                        
                        with db.engine.connect() as conn:
                            conn.execute(text("ALTER TABLE file ADD COLUMN physical_path VARCHAR(500) NULL"))
                            conn.commit()
                            
                        logger.info("physical_path字段添加成功")
                    else:
                        logger.info("File表已包含physical_path字段")
                        
                except Exception as e:
                    logger.error(f"修复File表失败: {str(e)}")
                    success = False
            else:
                logger.warning("File表不存在")
            
            if success:
                logger.info("✅ 数据库字段修复完成！")
                return True
            else:
                logger.error("❌ 部分字段修复失败")
                return False
                
    except ImportError as e:
        logger.error(f"导入应用模块失败: {str(e)}")
        logger.error("请确保在项目根目录运行此脚本，并且已安装所有依赖")
        return False
    except Exception as e:
        logger.error(f"修复过程中发生错误: {str(e)}")
        return False

def main():
    """主函数"""
    print("=== MySQL数据库字段修复工具 ===")
    print("正在修复UserSignature和File表的缺失字段...")
    print()
    
    success = fix_database_fields()
    
    print()
    if success:
        print("✅ 数据库字段修复成功！")
        print("请重启您的Flask应用程序以使更改生效。")
        print()
        print("重启命令示例:")
        print("sudo systemctl restart your-app-service")
        print("或者:")
        print("pkill -f gunicorn && nohup gunicorn -c gunicorn.conf.py run:app &")
    else:
        print("❌ 数据库字段修复失败！")
        print("请检查错误日志并确保:")
        print("1. MySQL服务正在运行")
        print("2. 数据库连接配置正确")
        print("3. 用户有足够的权限修改表结构")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
