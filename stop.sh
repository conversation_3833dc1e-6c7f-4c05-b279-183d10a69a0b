#!/bin/bash

# 设置工作目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &>/dev/null && pwd )"
cd "$SCRIPT_DIR"
# 检查 PID 文件
if [ -f ./run/gunicorn.pid ]; then
    pid=$(cat ./run/gunicorn.pid)
    
    # 检查进程是否存在
    if ps -p $pid > /dev/null; then
        echo "Stopping File Manager (PID: $pid)..."
        kill $pid
        sleep 2
        
        # 确认进程已停止
        if ps -p $pid > /dev/null; then
            echo "Process did not stop gracefully, forcing..."
            kill -9 $pid
        fi
        
        echo "File Manager stopped"
    else
        echo "Process not found"
    fi
    
    # 删除 PID 文件
    rm -f ./run/gunicorn.pid
else
    # 如果没有 PID 文件，尝试查找并杀死所有 gunicorn 进程
    echo "PID file not found, searching for gunicorn processes..."
    pkill -f "gunicorn.*run:app"
fi

# 检查端口是否已释放
if netstat -tlnp 2>/dev/null | grep -q ":2026 "; then
    echo "Warning: Port 2026 is still in use"
else
    echo "Port 2026 is free"
fi 