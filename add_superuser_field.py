#!/usr/bin/env python3
# 脚本用于直接向数据库中添加is_superuser字段

import sqlite3
import os
import sys
from flask import current_app

try:
    from app import create_app, db
    from app.models import User
except ImportError:
    print("无法导入应用程序包。请确保您在正确的目录中运行此脚本。")
    sys.exit(1)

def add_superuser_field():
    """
    直接向User表添加is_superuser字段
    """
    app = create_app()
    
    with app.app_context():
        try:
            # 使用Flask-SQLAlchemy配置的数据库URI
            db_uri = current_app.config['SQLALCHEMY_DATABASE_URI']
            print(f"使用数据库: {db_uri}")
            
            # 对于SQLite数据库，从URI中提取文件路径
            if db_uri.startswith('sqlite:///'):
                db_path = db_uri.replace('sqlite:///', '')
                
                # 检查路径是否正确
                if not os.path.isabs(db_path):
                    # 如果是相对路径，转换为绝对路径
                    db_path = os.path.join(os.getcwd(), db_path)
                    
                print(f"数据库文件路径: {db_path}")
                
                if not os.path.exists(db_path):
                    print(f"错误: 数据库文件不存在: {db_path}")
                    return False
                
                # 连接SQLite数据库
                print("正在连接数据库...")
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # 检查是否已存在此字段
                print("检查是否已存在is_superuser字段...")
                cursor.execute("PRAGMA table_info(user)")
                columns = [column[1] for column in cursor.fetchall()]
                
                if 'is_superuser' not in columns:
                    print("正在添加is_superuser字段...")
                    cursor.execute("ALTER TABLE user ADD COLUMN is_superuser BOOLEAN DEFAULT 0")
                    conn.commit()
                    print("成功添加is_superuser字段!")
                    
                    # 确保cv24051是超级用户
                    print("检查并设置cv24051为超级用户...")
                    cursor.execute("SELECT id FROM user WHERE username = 'cv24051'")
                    cv24051_user = cursor.fetchone()
                    
                    if cv24051_user:
                        cursor.execute("UPDATE user SET is_superuser = 1 WHERE username = 'cv24051'")
                        conn.commit()
                        print("已设置cv24051为超级用户!")
                    else:
                        print("警告: 未找到cv24051用户!")
                else:
                    print("is_superuser字段已存在，无需添加")
                
                conn.close()
                print("数据库操作完成")
                return True
            else:
                print(f"不支持的数据库类型: {db_uri}")
                print("此脚本仅支持SQLite数据库")
                return False
        
        except Exception as e:
            print(f"发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":
    print("开始执行添加is_superuser字段脚本...")
    success = add_superuser_field()
    if success:
        print("脚本执行成功!")
    else:
        print("脚本执行失败!")
        sys.exit(1) 