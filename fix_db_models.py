#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复数据库模型缺失字段
- 为UserSignature添加signature_id字段
- 为File添加physical_path字段
"""

import os
import sys
import importlib
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

try:
    # 尝试导入应用程序
    sys.path.insert(0, os.getcwd())
    from app import db
    from app.models import UserSignature, File, Signature
    
    # 检测数据库类型
    from sqlalchemy import inspect
    
    def fix_user_signature_model():
        """修复UserSignature模型，添加缺失的signature_id字段"""
        logger.info("开始修复UserSignature模型...")
        
        inspector = inspect(db.engine)
        user_signature_columns = [column['name'] for column in inspector.get_columns('user_signature')]
        
        if 'signature_id' not in user_signature_columns:
            logger.info("UserSignature模型缺少signature_id字段，正在添加...")
            
            # 添加signature_id列
            db.engine.execute("""
            ALTER TABLE user_signature ADD COLUMN signature_id INTEGER;
            """)
            
            # 添加外键约束
            if inspector.get_table_names().count('signature'):
                try:
                    db.engine.execute("""
                    ALTER TABLE user_signature 
                    ADD CONSTRAINT fk_user_signature_signature 
                    FOREIGN KEY (signature_id) REFERENCES signature (id);
                    """)
                    logger.info("已添加signature_id外键约束")
                except Exception as e:
                    logger.warning(f"添加外键约束失败，可能已存在: {str(e)}")
            
            logger.info("UserSignature模型修复完成")
            return True
        else:
            logger.info("UserSignature模型已包含signature_id字段，无需修复")
            return False
    
    def fix_file_model():
        """修复File模型，添加缺失的physical_path字段"""
        logger.info("开始修复File模型...")
        
        inspector = inspect(db.engine)
        file_columns = [column['name'] for column in inspector.get_columns('file')]
        
        if 'physical_path' not in file_columns:
            logger.info("File模型缺少physical_path字段，正在添加...")
            
            # 添加physical_path列
            db.engine.execute("""
            ALTER TABLE file ADD COLUMN physical_path VARCHAR(255);
            """)
            
            # 设置默认值为NULL
            logger.info("已添加physical_path字段，默认值为NULL")
            return True
        else:
            logger.info("File模型已包含physical_path字段，无需修复")
            return False

    def update_signature_data():
        """更新已有的签名数据，设置file_id字段"""
        logger.info("开始更新签名数据...")
        
        inspector = inspect(db.engine)
        signature_columns = [column['name'] for column in inspector.get_columns('signature')]
        
        if 'file_id' in signature_columns:
            try:
                # 查询缺少file_id的签名记录
                missing_file_id_query = db.session.query(Signature).filter(Signature.file_id == None)
                missing_count = missing_file_id_query.count()
                
                if missing_count > 0:
                    logger.info(f"发现{missing_count}条签名记录缺少file_id值")
                    
                    # 更新这些记录，设置默认file_id
                    # 这里我们根据UserSignature关联查找对应的文件，或者设置一个默认值
                    user_signatures = db.session.query(UserSignature).all()
                    
                    updated_count = 0
                    for user_sig in user_signatures:
                        if hasattr(user_sig, 'signature_id') and user_sig.signature_id is not None:
                            sig = db.session.query(Signature).filter(Signature.id == user_sig.signature_id).first()
                            if sig and sig.file_id is None:
                                # 查找该用户最近访问的文件作为默认值
                                recent_file = db.session.query(File).filter(File.user_id == user_sig.user_id).order_by(File.id.desc()).first()
                                if recent_file:
                                    sig.file_id = recent_file.id
                                    updated_count += 1
                    
                    if updated_count > 0:
                        db.session.commit()
                        logger.info(f"成功更新了{updated_count}条签名记录的file_id")
                else:
                    logger.info("所有签名记录都已有file_id值，无需更新")
            except Exception as e:
                db.session.rollback()
                logger.error(f"更新签名数据时出错: {str(e)}")
        else:
            logger.warning("Signature模型缺少file_id字段，请先修复该模型")

    def check_signature_table():
        """检查signature表是否存在，如果不存在则创建"""
        logger.info("检查signature表...")
        
        inspector = inspect(db.engine)
        tables = inspector.get_table_names()
        
        if 'signature' not in tables:
            logger.info("未找到signature表，正在创建...")
            
            # 创建signature表
            db.engine.execute("""
            CREATE TABLE signature (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                file_id INTEGER,
                signature_date DATETIME,
                signature_type VARCHAR(20),
                signature_position TEXT,
                signature_data TEXT,
                FOREIGN KEY(file_id) REFERENCES file(id)
            );
            """)
            
            logger.info("signature表创建成功")
            return True
        else:
            logger.info("signature表已存在")
            
            # 检查signature表中是否有file_id字段
            signature_columns = [column['name'] for column in inspector.get_columns('signature')]
            if 'file_id' not in signature_columns:
                logger.info("signature表缺少file_id字段，正在添加...")
                
                # 添加file_id列
                db.engine.execute("""
                ALTER TABLE signature ADD COLUMN file_id INTEGER;
                """)
                
                # 添加外键约束
                try:
                    db.engine.execute("""
                    ALTER TABLE signature 
                    ADD CONSTRAINT fk_signature_file 
                    FOREIGN KEY (file_id) REFERENCES file (id);
                    """)
                    logger.info("已添加file_id外键约束")
                except Exception as e:
                    logger.warning(f"添加外键约束失败，可能已存在: {str(e)}")
                
                return True
            else:
                logger.info("signature表的file_id字段已存在")
                return False
    
    def check_user_signature_table():
        """检查user_signature表是否存在，如果不存在则创建"""
        logger.info("检查user_signature表...")
        
        inspector = inspect(db.engine)
        tables = inspector.get_table_names()
        
        if 'user_signature' not in tables:
            logger.info("未找到user_signature表，正在创建...")
            
            # 创建user_signature表
            db.engine.execute("""
            CREATE TABLE user_signature (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                signature_id INTEGER,
                FOREIGN KEY(user_id) REFERENCES user(id),
                FOREIGN KEY(signature_id) REFERENCES signature(id)
            );
            """)
            
            logger.info("user_signature表创建成功")
            return True
        else:
            logger.info("user_signature表已存在")
            return False

    def main():
        """主函数，执行数据库修复"""
        logger.info("开始修复数据库模型...")
        
        try:
            # 创建数据库连接
            with db.app.app_context():
                # 检查和修复表
                check_signature_table()
                check_user_signature_table()
                
                # 修复模型
                us_fixed = fix_user_signature_model()
                file_fixed = fix_file_model()
                
                # 更新数据
                if us_fixed:
                    update_signature_data()
                
                if us_fixed or file_fixed:
                    logger.info("数据库模型修复完成，请重启应用程序")
                else:
                    logger.info("数据库模型无需修复")
                
                return True
        except Exception as e:
            logger.error(f"修复过程中发生错误: {str(e)}")
            return False

except ImportError as e:
    logger.error(f"导入应用模块失败，请确保您在项目根目录运行此脚本: {str(e)}")
    sys.exit(1)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 