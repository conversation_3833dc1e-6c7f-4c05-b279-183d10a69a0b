{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <h2 class="mb-4">系统操作日志</h2>
    
    <!-- 过滤表单 -->
    <form class="row g-3 mb-4" method="get">
        <div class="col-md-2">
            <select class="form-select" name="user_id">
                <option value="">所有用户</option>
                {% for user in users %}
                <option value="{{ user.id }}" {% if user.id == current_user_id %}selected{% endif %}>
                    {{ user.username }}
                </option>
                {% endfor %}
            </select>
        </div>
        
        <div class="col-md-2">
            <select class="form-select" name="action">
                <option value="">所有操作</option>
                {% for action_type in actions %}
                <option value="{{ action_type }}" {% if action_type == current_action %}selected{% endif %}>
                    {{ action_type }}
                </option>
                {% endfor %}
            </select>
        </div>
        
        <div class="col-md-2">
            <input type="date" class="form-control" name="start_date" value="{{ start_date or '' }}" placeholder="开始日期">
        </div>
        
        <div class="col-md-2">
            <input type="date" class="form-control" name="end_date" value="{{ end_date or '' }}" placeholder="结束日期">
        </div>
        
        <div class="col-md-2">
            <input type="text" class="form-control" name="search" value="{{ search or '' }}" placeholder="搜索...">
        </div>
        
        <div class="col-md-2">
            <button type="submit" class="btn btn-primary">筛选</button>
            <a href="{{ url_for('main.view_logs') }}" class="btn btn-secondary">重置</a>
        </div>
    </form>
    
    <!-- 日志表格 -->
    <div class="table-responsive">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>时间</th>
                    <th>用户</th>
                    <th>操作</th>
                    <th>详情</th>
                    <th>对象类型</th>
                    <th>对象ID</th>
                    <th>IP地址</th>
                </tr>
            </thead>
            <tbody>
                {% if logs %}
                    {% for log in logs %}
                    <tr>
                        <td>{{ log.timestamp|format_datetime if log.timestamp else '-' }}</td>
                        <td>
                            {% if log.user %}
                                {{ log.user.username }}
                            {% else %}
                                [已删除用户]
                            {% endif %}
                        </td>
                        <td>{{ log.action or '-' }}</td>
                        <td>{{ log.details or '-' }}</td>
                        <td>
                            {% if log.target_type %}
                                <span class="badge bg-info">{{ log.target_type }}</span>
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>
                            {% if log.target_id and log.target_type %}
                                {% if log.target_type == 'file' %}
                                    <a href="{{ url_for('main.preview_file', file_id=log.target_id) }}" target="_blank">
                                        {{ log.target_id }}
                                    </a>
                                {% elif log.target_type == 'folder' %}
                                    <a href="{{ url_for('main.index', folder_id=log.target_id) }}" target="_blank">
                                        {{ log.target_id }}
                                    </a>
                                {% else %}
                                    {{ log.target_id }}
                                {% endif %}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>{{ log.ip_address or '-' }}</td>
                    </tr>
                    {% endfor %}
                {% else %}
                    <tr>
                        <td colspan="7" class="text-center">暂无日志记录</td>
                    </tr>
                {% endif %}
            </tbody>
        </table>
    </div>
    
    <!-- 分页 -->
    {% if pagination and pagination.pages and pagination.pages > 1 %}
    <nav aria-label="Page navigation">
        <ul class="pagination justify-content-center">
            <!-- 首页和上一页 -->
            <li class="page-item {% if pagination.page == 1 %}disabled{% endif %}">
                <a class="page-link" href="{{ url_for('main.view_logs', page=1, user_id=current_user_id, action=current_action, start_date=start_date, end_date=end_date, search=search) }}">首页</a>
            </li>
            <li class="page-item {% if pagination.page == 1 %}disabled{% endif %}">
                <a class="page-link" href="{{ url_for('main.view_logs', page=pagination.page-1, user_id=current_user_id, action=current_action, start_date=start_date, end_date=end_date, search=search) }}">上一页</a>
            </li>
            
            <!-- 页码 -->
            {% if pagination.iter_pages %}
                {% for page in pagination.iter_pages() %}
                    {% if page %}
                        <li class="page-item {% if page == pagination.page %}active{% endif %}">
                            <a class="page-link" href="{{ url_for('main.view_logs', page=page, user_id=current_user_id, action=current_action, start_date=start_date, end_date=end_date, search=search) }}">{{ page }}</a>
                        </li>
                    {% else %}
                        <li class="page-item disabled"><span class="page-link">...</span></li>
                    {% endif %}
                {% endfor %}
            {% endif %}
            
            <!-- 下一页和尾页 -->
            <li class="page-item {% if pagination.page == pagination.pages %}disabled{% endif %}">
                <a class="page-link" href="{{ url_for('main.view_logs', page=pagination.page+1, user_id=current_user_id, action=current_action, start_date=start_date, end_date=end_date, search=search) }}">下一页</a>
            </li>
            <li class="page-item {% if pagination.page == pagination.pages %}disabled{% endif %}">
                <a class="page-link" href="{{ url_for('main.view_logs', page=pagination.pages, user_id=current_user_id, action=current_action, start_date=start_date, end_date=end_date, search=search) }}">尾页</a>
            </li>
        </ul>
    </nav>
    {% endif %}
</div>
{% endblock %} 