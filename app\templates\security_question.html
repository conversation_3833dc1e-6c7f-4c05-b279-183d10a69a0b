{% extends "base.html" %}
{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <h2>安全验证</h2>
        <p class="text-muted">您需要回答安全问题来完成登录</p>
        
        {% with messages = get_flashed_messages() %}
        {% if messages %}
        <div class="alert alert-danger">
            {{ messages[0] }}
        </div>
        {% endif %}
        {% endwith %}
        
        <form method="POST" action="{{ url_for('auth.verify_security_question', user_id=user.id) }}">
            <div class="mb-3">
                <label class="form-label">安全问题:</label>
                <p class="form-control-static"><strong>{{ user.security_question }}</strong></p>
            </div>
            <div class="mb-3">
                <label for="answer" class="form-label">您的答案:</label>
                <input type="text" class="form-control" id="answer" name="answer" required autofocus>
                <small class="form-text text-muted">答案不区分大小写</small>
            </div>
            <button type="submit" class="btn btn-primary">验证</button>
            <a href="{{ url_for('auth.login') }}" class="btn btn-outline-secondary">返回登录</a>
        </form>
    </div>
</div>
{% endblock %} 