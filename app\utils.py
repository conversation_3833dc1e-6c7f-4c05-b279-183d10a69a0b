from flask import request, current_app, session
from app import db
from datetime import datetime
import random
import string
import io
import base64
from PIL import Image, ImageDraw, ImageFont, ImageFilter

def is_super_admin(user):
    """检查用户是否是超级管理员
    
    参数:
        user: 用户对象
        
    返回:
        如果用户是超级管理员，则返回True，否则返回False
    """
    if not user:
        return False
        
    # 用户名为'cv24051'的用户是超级管理员
    if user.username == 'cv24051':
        return True
        
    # 如果用户有is_superuser字段并且为True，则是超级管理员
    if hasattr(user, 'is_superuser') and user.is_superuser:
        return True
        
    return False
    
def is_owner(user, obj):
    """检查用户是否是对象的所有者
    
    参数:
        user: 用户对象
        obj: 任何带有user_id字段的对象
        
    返回:
        如果用户是对象的所有者，则返回True，否则返回False
    """
    if not user or not obj:
        return False
        
    return obj.user_id == user.id

def log_user_action(user, action, details, target_id=None, target_type=None):
    """记录用户操作
    
    参数:
        user: 当前用户对象
        action: 操作类型 (LOGIN, LOGOUT, UPLOAD, DOWNLOAD等)
        details: 操作详情描述
        target_id: 操作对象的ID，如文件ID、文件夹ID等（可选）
        target_type: 操作对象的类型，如file, folder, user等（可选）
    """
    try:
        # Import UserLog inside the function to avoid circular import
        from app.models import UserLog
        
        log = UserLog(
            user_id=user.id,
            action=action,
            details=details,
            ip_address=request.remote_addr,
            target_id=target_id,
            target_type=target_type
        )
        db.session.add(log)
        db.session.commit()
    except Exception as e:
        current_app.logger.error(f'记录用户操作失败: {str(e)}')
        db.session.rollback()

def can_access_feature(user, feature):
    """检查用户是否可以访问特定功能
    
    参数:
        user: 用户对象
        feature: 功能名称，可以是：'user_management', 'system_logs', 'keyword_management', 'user_group_management'
        
    返回:
        如果用户可以访问该功能，则返回True，否则返回False
    """
    if not user:
        return False
        
    # 超级管理员可以访问所有功能
    if is_super_admin(user):
        return True
        
    # 高级用户可以访问所有指定的功能
    if user.is_admin:
        advanced_features = [
            'user_management',
            'system_logs',
            'keyword_management',
            'user_group_management'
        ]
        return feature in advanced_features
        
    # 普通用户只能访问关键字管理
    regular_features = ['keyword_management']
    return feature in regular_features

# 定义翻译词典
_translations = {
    'zh': {
        # 标题和页面文本
        'File Management System': '文件管理系统',
        'User Group Management': '用户组管理',
        'User Groups': '用户组',
        'Group Name': '组名称',
        'Description': '描述',
        'Members': '成员',
        'Permissions': '权限',
        'Storage Limit': '存储限制',
        'Created By': '创建者',
        'Create Date': '创建日期',
        'Created Date': '创建日期',
        'Actions': '操作',
        'No groups found': '未找到用户组',
        'Can Upload Files': '可上传文件',
        'Can Download Files': '可下载文件',
        'Can Delete Files': '可删除文件',
        'Can Share Files': '可分享文件',
        'Unlimited': '无限制',
        'MB': 'MB',
        'Manage Members': '管理成员',
        'Edit': '编辑',
        'Delete': '删除',
        'Include subfolders': '包含子文件夹',
        'All folders': '所有文件夹',
        'Current folder': '当前文件夹',
        'Allowed User Groups': '允许访问的用户组',
        'Hold Ctrl key to select multiple. Members of selected groups will have access to this folder': '按住Ctrl键可多选。选中用户组的成员将可以访问此文件夹',
        'Selected folder': '已选文件夹',
        'Select folder to filter': '选择要筛选的文件夹',
        'Administrator Group': '管理员组',
        'Members will have administrator privileges': '成员将拥有管理员权限',
        'Group Members': '组成员',
        'Storage Settings': '存储设置',
        'Storage Limit (MB)': '存储限制（MB）',
        '0 = Unlimited': '0 = 无限制',
        'Create User Group': '创建用户组',
        'All folders include subfolders': '所有文件夹（包含子文件夹）',
        'Hold Ctrl key to select multiple users': '按住Ctrl键可多选用户',
        'Upload Files': '上传文件',
        'File Upload': '文件上传',
        'Folder Upload': '文件夹上传',
        'Upload Folder': '上传文件夹',
        'Tags': '标签',
        'Separate with commas': '使用逗号分隔',
        'Upload': '上传',
        'Click to select files': '点击选择文件',
        'Click to select folder': '点击选择文件夹',
        'Folder structure will be preserved': '文件夹结构将被保留',
        'Clear selection': '清除选择',
        'User Management': '用户管理',
        'System Logs': '系统日志',
        'Keyword Management': '关键字管理',
        'Trash': '回收站',
        'Change Password': '修改密码',
        'Logout': '退出登录',
        'Home': '首页',
        'Login': '登录',
        'Support multiple file uploads': '支持多文件上传',
        'Selected': '选择',
        'items': '个',
        
        # 按钮和操作文本
        'Submit': '提交',
        'Cancel': '取消',
        'Save': '保存',
        'Delete': '删除',
        'Edit': '编辑',
        'Add': '添加',
        'Search': '搜索',
        'Reset': '重置',
        'Download': '下载',
        'Preview': '预览',
        'Move': '移动',
        'Copy': '复制',
        
        # 表单标签
        'Username': '用户名',
        'Password': '密码',
        'Confirm Password': '确认密码',
        'Old Password': '原密码',
        'New Password': '新密码',
        'File': '文件',
        'Folder': '文件夹',
        'Name': '名称',
        'Type': '类型',
        'Size': '大小',
        'Date': '日期',
        'Owner': '所有者',
        'Description': '描述',
        
        # 状态和消息
        'Loading...': '加载中...',
        'Success': '成功',
        'Error': '错误',
        'Warning': '警告',
        'Please confirm': '请确认',
        'No data available': '无可用数据',
        'No files found': '未找到文件',
        'No folders found': '未找到文件夹',
        'File already exists': '文件已存在',
        'Folder already exists': '文件夹已存在',
        'Invalid file name': '无效的文件名',
        'Invalid folder name': '无效的文件夹名',
        'Operation successful': '操作成功',
        'Operation failed': '操作失败',
        
        # 验证码
        'Verification Code': '验证码',
        'Click to refresh': '点击刷新',
        'Please enter the verification code': '请输入验证码',
        'Verification code error': '验证码错误',
        
        # 安全问题
        'Security Question': '安全问题',
        'Security Answer': '安全问题答案',
        'Security Verification': '安全验证',
        'Please answer the security question': '请回答安全问题',
        'Answer is case-insensitive': '答案不区分大小写',
        
        # 文件操作
        'Upload Files': '上传文件',
        'Create Folder': '创建文件夹',
        'Delete Files': '删除文件',
        'Move Files': '移动文件',
        'Download Files': '下载文件',
        'Batch Operation': '批量操作',
        'New Folder': '新建文件夹',
        'Folder Name': '文件夹名称',
        'Public Access': '公开访问',
        'Read Only': '只读模式',
        'Allowed Users': '允许访问的用户',
        'Hold Ctrl key to select multiple': '按住Ctrl键可多选',
        'Folder Permission Settings': '文件夹权限设置',
        'Allow all users to access this folder': '允许所有用户访问此文件夹',
        'Other users can only view, not modify folder contents': '其他用户只能查看，不能修改文件夹内容',
        'Hold Ctrl key to select multiple. Selected users will have access to this folder': '按住Ctrl键可多选。选中的用户将可以访问此文件夹',
        'Create': '创建',
        'Permissions': '权限',
        'Public folder': '公开文件夹',
        'Read only': '只读',
        'Authorized access': '已授权访问',
        'Admin visible': '管理员可见',
        'Add tag': '添加标签',
        'Tag Management': '标签管理',
        'Modified Date': '修改日期',
        'Select All': '全选',
        'Manage Tags': '管理标签',
        'Actions': '操作',
        'Move To': '移动到',
        'Advanced Filter': '高级筛选',
        'Select Folder': '选择文件夹',
        'All Folders': '所有文件夹',
        'Select folder to filter': '选择要筛选的文件夹',
        'Keyword': '关键词',
        'Enter or select first keyword': '输入或选择第一个关键词',
        'Enter or select second keyword': '输入或选择第二个关键词',
        'Filter': '筛选',
        'Reset': '重置',
        'Filter Results': '筛选结果',
        'contains': '包含',
        'equals': '等于',
        'Search scope': '搜索范围',
        'Including subfolders': '包含子文件夹',
        'Current folder': '当前文件夹',
        'Clear Filter': '清除筛选',
        'Filename': '文件名',
        'Fuzzy': '模糊',
        'Exact': '精确',
        'Tags (comma separated)': '标签（逗号分隔）',
        'Enter multiple tags, separated by commas': '输入多个标签，用逗号分隔',
        'Root directory': '根目录',
        
        # 新增翻译项
        'Please select files or folders to add tags': '请选择要添加标签的文件或文件夹',
        'Please select files or folders to move': '请选择要移动的文件或文件夹',
        'Please select target folder': '请选择目标文件夹',
        'Enter new tags, separate with commas': '输入新标签，多个标签用逗号分隔',
        'Add Tags': '添加标签',
        'Existing Tags': '已有标签',
        'Delete Selected Tags': '删除选中标签',
        'Move to Folder': '移动到文件夹',
        'Move File': '移动文件',
        'Select Target Folder': '选择目标文件夹',
        'Root Directory': '根目录',
        
        # JavaScript消息
        'Are you sure you want to move this file to trash?': '确定要将此文件移动到回收站吗？',
        'Delete failed': '删除失败',
        'Operation failed': '操作失败',
        'Please enter folder name': '请输入文件夹名称',
        'Create failed': '创建失败',
        'Are you sure you want to delete this folder? All contents in the folder will be deleted!': '确定要删除此文件夹吗？文件夹内的所有内容都将被删除！',
        'Are you sure you want to move the selected': '确定要将选中的',
        'folders': '个文件夹',
        'and': '和',
        'files': '个文件',
        'to trash?': '移动到回收站吗？',
        'Please select files or folders to download': '请选择要下载的文件或文件夹',
        'Download failed': '下载失败',
        'Please select tags to delete': '请选择要删除的标签',
        'Are you sure you want to delete': '确定要删除选中的',
        'selected tags? This will remove these tags from all files and folders.': '个标签吗？这将从所有文件和文件夹中移除这些标签。',
        
        # 权限相关
        'Advanced User': '高级用户',
        'Regular User': '普通用户',
        'Advanced User Permissions': '高级用户权限',
        'Regular User Permissions': '普通用户权限',
        'Advanced users can access': '高级用户可以访问',
        'Regular users can access': '普通用户可以访问',
        'Advanced users have access to': '高级用户可以访问以下功能：',
        'Regular users have access to': '普通用户可以访问以下功能：',
        'User Management': '用户管理',
        'System Logs': '系统日志',
        'Keyword Management': '关键字管理',
        'User Group Management': '用户组管理',
        'Permission denied': '权限不足',
        'You do not have permission to access this feature': '您没有权限访问此功能',
        'Contact administrator for access': '请联系管理员获取权限',
        'Advanced users can access User Management, System Logs, Keyword Management, and User Group Management': '高级用户可以访问：用户管理、系统日志、关键字管理、用户组管理',
        'Regular users can only access Keyword Management': '普通用户仅可以访问：关键字管理',
        'Your current permission level': '您当前的权限级别',
        'Available features': '可用功能',
        
        # 关键字管理相关
        'Add New Keyword': '添加新关键字',
        'Add Keyword 1': '添加关键字1',
        'Add Keyword 2': '添加关键字2',
        'Keyword': '关键字',
        'Keyword 1': '关键字1',
        'Keyword 2': '关键字2',
        'Keyword Type': '关键字类型',
        'Global (All Folders)': '全局（所有文件夹）',
        'Choose a folder or keep it global': '选择关联文件夹或保持全局',
        'General': '通用',
        'You can create global keywords or associate keywords with specific folders': '您可以创建全局关键字或为特定文件夹关联关键字',
        'This keyword 1 already exists for the selected folder': '此关键字1已存在于选择的文件夹',
        'This keyword 2 already exists for the selected folder': '此关键字2已存在于选择的文件夹',
        'Keyword 1 added successfully': '关键字1添加成功',
        'Keyword 2 added successfully': '关键字2添加成功',
        'Will appear in the first keyword dropdown when filtering': '筛选时将显示在第一个关键字下拉框中',
        'Will appear in the second keyword dropdown when filtering': '筛选时将显示在第二个关键字下拉框中',
        'Filter by Folder': '按文件夹筛选',
        'Show All': '显示全部',
        'Global Keywords Only': '仅全局关键字',
        'All Keywords': '所有关键字',
        'Keyword 1 Only': '仅关键字1',
        'Keyword 2 Only': '仅关键字2',
        'Folder': '文件夹',
        'Global': '全局',
        'Created Date': '创建日期',
        'Actions': '操作',
        'Delete': '删除',
        'Are you sure you want to delete this keyword?': '确定要删除此关键字吗？',
        'Delete failed': '删除失败',
        'Operation failed': '操作失败',
        'No keywords found': '未找到关键字',
    },
    'en': {
        # 标题和页面文本
        'File Management System': 'File Management System',
        'Upload Files': 'Upload Files',
        'File Upload': 'File Upload',
        'Folder Upload': 'Folder Upload',
        'Tags': 'Tags',
        'Separate with commas': 'Separate with commas',
        'Upload': 'Upload',
        'Click to select files': 'Click to select files',
        'Click to select folder': 'Click to select folder',
        'Folder structure will be preserved': 'Folder structure will be preserved',
        'Clear selection': 'Clear selection',
        'User Management': 'User Management',
        'System Logs': 'System Logs',
        'Keyword Management': 'Keyword Management',
        'Trash': 'Trash',
        'Change Password': 'Change Password',
        'Logout': 'Logout',
        'Home': 'Home',
        'Login': 'Login',
        'Support multiple file uploads': 'Support multiple file uploads',
        'Selected': 'Selected',
        'items': 'items',
        
        # 按钮和操作文本
        'Submit': 'Submit',
        'Cancel': 'Cancel',
        'Save': 'Save',
        'Delete': 'Delete',
        'Edit': 'Edit',
        'Add': 'Add',
        'Search': 'Search',
        'Reset': 'Reset',
        'Download': 'Download',
        'Preview': 'Preview',
        'Move': 'Move',
        'Copy': 'Copy',
        
        # 表单标签
        'Username': 'Username',
        'Password': 'Password',
        'Confirm Password': 'Confirm Password',
        'Old Password': 'Old Password',
        'New Password': 'New Password',
        'File': 'File',
        'Folder': 'Folder',
        'Name': 'Name',
        'Type': 'Type',
        'Size': 'Size',
        'Date': 'Date',
        'Owner': 'Owner',
        'Description': 'Description',
        
        # 状态和消息
        'Loading...': 'Loading...',
        'Success': 'Success',
        'Error': 'Error',
        'Warning': 'Warning',
        'Please confirm': 'Please confirm',
        'No data available': 'No data available',
        'No files found': 'No files found',
        'No folders found': 'No folders found',
        'File already exists': 'File already exists',
        'Folder already exists': 'Folder already exists',
        'Invalid file name': 'Invalid file name',
        'Invalid folder name': 'Invalid folder name',
        'Operation successful': 'Operation successful',
        'Operation failed': 'Operation failed',
        
        # 验证码
        'Verification Code': 'Verification Code',
        'Click to refresh': 'Click to refresh',
        'Please enter the verification code': 'Please enter the verification code',
        'Verification code error': 'Verification code error',
        
        # 安全问题
        'Security Question': 'Security Question',
        'Security Answer': 'Security Answer',
        'Security Verification': 'Security Verification',
        'Please answer the security question': 'Please answer the security question',
        'Answer is case-insensitive': 'Answer is case-insensitive',
        
        # 文件操作
        'Upload Files': 'Upload Files',
        'Create Folder': 'Create Folder',
        'Delete Files': 'Delete Files',
        'Move Files': 'Move Files',
        'Download Files': 'Download Files',
        'Batch Operation': 'Batch Operation',
        
        # 新增翻译项 - 英文部分
        'New Folder': 'New Folder',
        'Folder Name': 'Folder Name',
        'Public Access': 'Public Access',
        'Read Only': 'Read Only',
        'Allowed Users': 'Allowed Users',
        'Hold Ctrl key to select multiple': 'Hold Ctrl key to select multiple',
        'Folder Permission Settings': 'Folder Permission Settings',
        'Allow all users to access this folder': 'Allow all users to access this folder',
        'Other users can only view, not modify folder contents': 'Other users can only view, not modify folder contents',
        'Hold Ctrl key to select multiple. Selected users will have access to this folder': 'Hold Ctrl key to select multiple. Selected users will have access to this folder',
        'Create': 'Create',
        'Permissions': 'Permissions',
        'Public folder': 'Public folder',
        'Read only': 'Read only',
        'Authorized access': 'Authorized access',
        'Admin visible': 'Admin visible',
        'Add tag': 'Add tag',
        'Tag Management': 'Tag Management',
        'Modified Date': 'Modified Date',
        'Select All': 'Select All',
        'Manage Tags': 'Manage Tags',
        'Actions': 'Actions',
        'Move To': 'Move To',
        'Advanced Filter': 'Advanced Filter',
        'Select Folder': 'Select Folder',
        'All Folders': 'All Folders',
        'Select folder to filter': 'Select folder to filter',
        'Keyword': 'Keyword',
        'Enter or select first keyword': 'Enter or select first keyword',
        'Enter or select second keyword': 'Enter or select second keyword',
        'Filter': 'Filter',
        'Filter Results': 'Filter Results',
        'contains': 'contains',
        'equals': 'equals',
        'Search scope': 'Search scope',
        'Including subfolders': 'Including subfolders',
        'Current folder': 'Current folder',
        'Clear Filter': 'Clear Filter',
        'Filename': 'Filename',
        'Fuzzy': 'Fuzzy',
        'Exact': 'Exact',
        'Tags (comma separated)': 'Tags (comma separated)',
        'Enter multiple tags, separated by commas': 'Enter multiple tags, separated by commas',
        'Root directory': 'Root directory',
        'Root Directory': 'Root Directory',
        
        # JavaScript 消息 - 英文部分
        'Please select files or folders to add tags': 'Please select files or folders to add tags',
        'Please select files or folders to move': 'Please select files or folders to move',
        'Please select target folder': 'Please select target folder',
        'Enter new tags, separate with commas': 'Enter new tags, separate with commas',
        'Add Tags': 'Add Tags',
        'Existing Tags': 'Existing Tags',
        'Delete Selected Tags': 'Delete Selected Tags',
        'Move to Folder': 'Move to Folder',
        'Move File': 'Move File',
        'Select Target Folder': 'Select Target Folder',
        'Are you sure you want to move this file to trash?': 'Are you sure you want to move this file to trash?',
        'Delete failed': 'Delete failed',
        'Operation failed': 'Operation failed',
        'Please enter folder name': 'Please enter folder name',
        'Create failed': 'Create failed',
        'Are you sure you want to delete this folder? All contents in the folder will be deleted!': 'Are you sure you want to delete this folder? All contents in the folder will be deleted!',
        'Are you sure you want to move the selected': 'Are you sure you want to move the selected',
        'folders': 'folders',
        'and': 'and',
        'files': 'files',
        'to trash?': 'to trash?',
        'Please select files or folders to download': 'Please select files or folders to download',
        'Download failed': 'Download failed',
        'Please select tags to delete': 'Please select tags to delete',
        'Are you sure you want to delete': 'Are you sure you want to delete',
        'selected tags? This will remove these tags from all files and folders.': 'selected tags? This will remove these tags from all files and folders.',
    }
}

def get_text(key, lang=None):
    """获取指定语言的文本
    
    参数:
        key: 文本键
        lang: 语言代码，如果未提供，则使用当前会话的语言或默认语言
        
    返回:
        翻译后的文本，如果没有找到翻译，则返回键本身
    """
    if lang is None:
        lang = session.get('language', 'zh')
    
    # 如果指定语言没有该翻译，使用英文作为后备
    if lang not in _translations or key not in _translations[lang]:
        return key
    
    return _translations[lang][key]

def generate_captcha(width=120, height=40, length=4):
    """
    生成验证码图片和对应的文本
    
    参数:
    width -- 图片宽度
    height -- 图片高度
    length -- 验证码长度
    
    返回:
    (text, image_base64) -- 验证码文本和base64编码的图片
    """
    # 字符集，去除容易混淆的字符
    characters = string.ascii_uppercase + string.digits
    characters = characters.replace('0', '').replace('O', '')
    characters = characters.replace('1', '').replace('I', '')
    
    # 生成随机验证码文本
    captcha_text = ''.join(random.choice(characters) for _ in range(length))
    
    # 创建图片对象 - 使用浅蓝色背景
    image = Image.new('RGB', (width, height), color=(240, 248, 255))
    draw = ImageDraw.Draw(image)
    
    # 字体尺寸
    font_size = height - 20  # 自适应字体大小
    
    # 根据系统环境尝试加载不同的字体
    try:
        # 尝试常见的系统字体
        common_fonts = [
            'arial.ttf',
            '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',  # Linux
            '/usr/share/fonts/TTF/DejaVuSans.ttf',              # Linux
            '/System/Library/Fonts/Geneva.ttf',                 # macOS
            'C:\\Windows\\Fonts\\arial.ttf',                    # Windows
            'C:\\Windows\\Fonts\\Arial.ttf'                     # Windows
        ]
        
        font = None
        for font_path in common_fonts:
            try:
                font = ImageFont.truetype(font_path, font_size)
                break
            except IOError:
                continue
                
        if font is None:
            font = ImageFont.load_default()
    except Exception:
        font = ImageFont.load_default()
    
    # 绘制文本 - 使用深蓝色
    text_width = draw.textlength(captcha_text, font=font)
    text_x = (width - text_width) / 2
    text_y = (height - font_size) / 2
    draw.text((text_x, text_y), captcha_text, font=font, fill=(0, 0, 139))
    
    # 添加较少的干扰线 - 使用浅灰色
    for _ in range(3):
        start_x = random.randint(0, width - 1)
        start_y = random.randint(0, height - 1)
        end_x = random.randint(0, width - 1)
        end_y = random.randint(0, height - 1)
        draw.line([(start_x, start_y), (end_x, end_y)], fill=(200, 200, 200), width=1)
    
    # 添加较少的干扰点 - 使用浅灰色
    for _ in range(30):
        x = random.randint(0, width - 1)
        y = random.randint(0, height - 1)
        draw.point([x, y], fill=(200, 200, 200))
    
    # 不使用模糊滤镜，保持文字清晰
    
    # 转换为base64编码
    buffered = io.BytesIO()
    image.save(buffered, format="PNG")
    img_str = base64.b64encode(buffered.getvalue()).decode()
    
    return captcha_text, img_str 