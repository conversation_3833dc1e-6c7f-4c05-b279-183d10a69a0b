from app import create_app, db
from sqlalchemy import text

app = create_app()
with app.app_context():
    try:
        # 添加新表和新列
        with db.engine.connect() as conn:
            # 检查Keyword表是否存在
            result = conn.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='keyword'"))
            if not result.fetchone():
                print("创建 keyword 表...")
                conn.execute(text('''
                    CREATE TABLE keyword (
                        id INTEGER NOT NULL, 
                        word VARCHAR(50) NOT NULL, 
                        created_by INTEGER NOT NULL, 
                        create_date DATETIME,
                        folder_id INTEGER,
                        keyword_type INTEGER DEFAULT 0,
                        PRIMARY KEY (id), 
                        FOREIGN KEY(created_by) REFERENCES user (id),
                        FOREIGN KEY(folder_id) REFERENCES folder (id)
                    )
                '''))
            else:
                # 检查folder_id列是否存在
                result = conn.execute(text("PRAGMA table_info(keyword)"))
                columns = [row[1] for row in result.fetchall()]
                if 'folder_id' not in columns:
                    print("为keyword表添加folder_id字段...")
                    conn.execute(text('ALTER TABLE keyword ADD COLUMN folder_id INTEGER'))
                
                if 'keyword_type' not in columns:
                    print("为keyword表添加keyword_type字段...")
                    conn.execute(text('ALTER TABLE keyword ADD COLUMN keyword_type INTEGER DEFAULT 0'))
            
            # 检查列是否已存在
            result = conn.execute(text("PRAGMA table_info(folder)"))
            columns = [row[1] for row in result.fetchall()]
            
            print("当前表结构:", columns)
            
            # 只添加不存在的列
            if 'is_public' not in columns:
                print("添加 is_public 字段...")
                conn.execute(text('ALTER TABLE folder ADD COLUMN is_public BOOLEAN DEFAULT 0'))
            
            if 'allowed_users' not in columns:
                print("添加 allowed_users 字段...")
                conn.execute(text('ALTER TABLE folder ADD COLUMN allowed_users TEXT DEFAULT ""'))
            
            if 'read_only' not in columns:
                print("添加 read_only 字段...")
                conn.execute(text('ALTER TABLE folder ADD COLUMN read_only BOOLEAN DEFAULT 0'))
            
            db.session.commit()
        
        print("数据库更新成功")
        
        # 显示更新后的表结构
        with db.engine.connect() as conn:
            result = conn.execute(text("PRAGMA table_info(folder)"))
            print("\n更新后的文件夹表结构:")
            for row in result:
                print(f"字段: {row[1]}, 类型: {row[2]}, 允许空: {row[3]}, 默认值: {row[4]}")
            
            # 显示keyword表结构
            result = conn.execute(text("PRAGMA table_info(keyword)"))
            print("\n关键字表结构:")
            for row in result:
                print(f"字段: {row[1]}, 类型: {row[2]}, 允许空: {row[3]}, 默认值: {row[4]}")
            
    except Exception as e:
        print(f"更新失败: {str(e)}") 