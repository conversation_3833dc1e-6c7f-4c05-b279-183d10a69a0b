{% extends "base.html" %}
{% block content %}
<div class="container-fluid p-0">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="{{ url_for('main.index') }}">
                        <i class="fas fa-home home-icon"></i>
                        {{ t('Home') }}
                    </a>
                </li>
                <li class="breadcrumb-item active">{{ t('Electronic Signature') }}</li>
                <li class="breadcrumb-item active">{{ file.original_filename }}</li>
            </ol>
        </nav>
    </div>

    <!-- 数据库结构警告 -->
    <div class="alert alert-warning alert-dismissible fade show" role="alert" id="db-structure-warning" style="display: none;">
        <strong>{{ t('Warning') }}!</strong> {{ t('The database structure needs to be updated. Signature functionality may not work properly. Please contact your administrator.') }}
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">{{ t('Add Your Signature') }}</h5>
                    <div class="btn-group">
                        <a href="{{ url_for('main.user_signatures') }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-signature"></i> {{ t('Manage My Signatures') }}
                        </a>
                        <a href="{{ url_for('main.index', folder_id=file.folder_id) }}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> {{ t('Back') }}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="file-info mb-4">
                        <h6>{{ t('File Information') }}:</h6>
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <tr>
                                    <th>{{ t('Filename') }}</th>
                                    <td>{{ file.original_filename }}</td>
                                </tr>
                                <tr>
                                    <th>{{ t('File Type') }}</th>
                                    <td>{{ file.file_type }}</td>
                                </tr>
                                <tr>
                                    <th>{{ t('File Size') }}</th>
                                    <td>{{ file.file_size|format_size }}</td>
                                </tr>
                                <tr>
                                    <th>{{ t('Upload Date') }}</th>
                                    <td>{{ file.upload_date|format_datetime }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- 默认签名选择区域 -->
                    {% if user_signatures %}
                    <div class="user-signatures-container mb-4">
                        <h6>{{ t('Use Your Saved Signatures') }}:</h6>
                        <div class="user-signatures-wrapper d-flex flex-wrap gap-3 p-3 border rounded bg-light">
                            {% for signature in user_signatures %}
                            <div class="user-signature-option" data-signature-id="{{ signature.id }}">
                                <div class="signature-image mb-2 p-2 bg-white rounded border {% if signature.is_default %}border-success{% else %}border-light{% endif %}" style="cursor: pointer;">
                                    <img src="{{ signature.signature_data }}" alt="{{ t('Signature') }}" class="img-fluid" style="max-height: 80px;">
                                </div>
                                <div class="text-center small">
                                    {% if signature.description %}{{ signature.description }}{% else %}{{ t('Signature') + ' #' + signature.id|string }}{% endif %}
                                    {% if signature.is_default %}<span class="badge bg-success">{{ t('Default') }}</span>{% endif %}
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        <div class="mt-2">
                            <button id="useSelectedSignature" class="btn btn-primary btn-sm" disabled>
                                <i class="fas fa-check"></i> {{ t('Use Selected Signature') }}
                            </button>
                            <span class="text-muted ms-2 small">{{ t('Click on a signature to select it') }}</span>
                        </div>
                    </div>
                    
                    <div class="separator mb-4 text-center">
                        <span class="bg-white px-3 text-muted">{{ t('OR') }}</span>
                        <hr class="position-relative" style="top: -10px; z-index: -1;">
                    </div>
                    {% endif %}

                    <div class="signature-pad-container mb-4">
                        <h6>{{ t('Draw Your Signature Below') }}:</h6>
                        <div class="signature-pad-wrapper">
                            <canvas id="signaturePad" class="signature-pad"></canvas>
                        </div>
                        <div class="signature-pad-controls mt-2 d-flex gap-2">
                            <button id="clearSignature" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-eraser"></i> {{ t('Clear') }}
                            </button>
                            <button id="saveSignature" class="btn btn-primary btn-sm">
                                <i class="fas fa-save"></i> {{ t('Save Signature') }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{{ t('Existing Signatures') }}</h5>
                </div>
                <div class="card-body">
                    <div id="signaturesList">
                        {% if signatures %}
                            {% for signature in signatures %}
                            <div class="signature-item mb-3 p-3 border rounded">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <div>
                                        <strong>{{ t('Signed by') }}:</strong> {{ signature.user.username }}
                                    </div>
                                    <div class="signature-date">
                                        {{ signature.signature_date|format_datetime }}
                                    </div>
                                </div>
                                <div class="signature-image mb-2">
                                    <img src="{{ signature.signature_data }}" alt="{{ t('Signature') }}" class="img-fluid">
                                </div>
                                <div class="signature-actions d-flex gap-2">
                                    <button class="btn btn-sm btn-outline-info verify-signature" data-signature-id="{{ signature.id }}">
                                        <i class="fas fa-check-circle"></i> {{ t('Verify') }}
                                    </button>
                                    {% if current_user.is_admin or current_user.id == signature.user_id %}
                                    <button class="btn btn-sm btn-outline-danger delete-signature" data-signature-id="{{ signature.id }}">
                                        <i class="fas fa-trash"></i> {{ t('Delete') }}
                                    </button>
                                    {% endif %}
                                </div>
                            </div>
                            {% endfor %}
                        {% else %}
                            <div class="alert alert-info">
                                {{ t('No signatures have been added to this file yet.') }}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 签名验证模态框 -->
<div class="modal fade" id="signatureVerifyModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ t('Signature Verification') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="signatureVerifyContent">
                <!-- 验证内容将在这里动态加载 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ t('Close') }}</button>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteSignatureModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ t('Confirm Deletion') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                {{ t('Are you sure you want to delete this signature?') }}
                <input type="hidden" id="signatureIdToDelete">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ t('Cancel') }}</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteSignature">{{ t('Delete') }}</button>
            </div>
        </div>
    </div>
</div>

<style>
.signature-pad-wrapper {
    border: 1px solid #ddd;
    border-radius: 4px;
    position: relative;
}

.signature-pad {
    width: 100%;
    height: 200px;
    background-color: #fff;
    border-radius: 4px;
}

.signature-item {
    background-color: #f9f9f9;
}

.signature-image img {
    max-height: 100px;
    border: 1px solid #eee;
    border-radius: 4px;
    background-color: #fff;
}

.user-signature-option.selected .signature-image {
    border-color: #0d6efd !important;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}
</style>

<script src="https://cdn.jsdelivr.net/npm/signature_pad@4.0.0/dist/signature_pad.umd.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 初始化签名板
    const canvas = document.getElementById('signaturePad');
    const signaturePad = new SignaturePad(canvas, {
        backgroundColor: 'rgb(255, 255, 255)',
        penColor: 'rgb(0, 0, 0)'
    });
    
    // 自适应调整画布大小
    function resizeCanvas() {
        const ratio = Math.max(window.devicePixelRatio || 1, 1);
        canvas.width = canvas.offsetWidth * ratio;
        canvas.height = canvas.offsetHeight * ratio;
        canvas.getContext("2d").scale(ratio, ratio);
        signaturePad.clear(); // 调整大小后清除
    }
    
    window.addEventListener("resize", resizeCanvas);
    resizeCanvas();
    
    // 清除签名
    document.getElementById('clearSignature').addEventListener('click', function() {
        signaturePad.clear();
    });
    
    // 用户签名选择逻辑
    let selectedSignatureId = null;
    const userSignatureOptions = document.querySelectorAll('.user-signature-option');
    const useSelectedSignatureBtn = document.getElementById('useSelectedSignature');
    
    if (userSignatureOptions.length > 0) {
        userSignatureOptions.forEach(option => {
            option.addEventListener('click', function() {
                // 移除所有已选中的样式
                userSignatureOptions.forEach(opt => opt.classList.remove('selected'));
                
                // 添加选中样式
                this.classList.add('selected');
                
                // 保存选中的签名ID
                selectedSignatureId = this.getAttribute('data-signature-id');
                
                // 启用"使用选中签名"按钮
                useSelectedSignatureBtn.removeAttribute('disabled');
            });
        });
        
        // 使用选中的签名
        useSelectedSignatureBtn.addEventListener('click', function() {
            if (!selectedSignatureId) return;
            
            // 创建表单数据
            const formData = new FormData();
            formData.append('user_signature_id', selectedSignatureId);
            
            // 发送签名数据到服务器
            fetch('{{ url_for("main.add_signature", file_id=file.id) }}', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('{{ t("Signature saved successfully") }}');
                    location.reload(); // 重新加载页面显示新签名
                } else {
                    alert(data.message || '{{ t("Failed to save signature") }}');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('{{ t("An error occurred while saving the signature") }}');
            });
        });
    }
    
    // 保存签名
    document.getElementById('saveSignature').addEventListener('click', function() {
        if (signaturePad.isEmpty()) {
            alert('{{ t("Please provide a signature first") }}');
            return;
        }
        
        const signatureData = signaturePad.toDataURL();
        const metadata = JSON.stringify({
            browser: navigator.userAgent,
            timestamp: new Date().toISOString(),
            screen_size: `${window.screen.width}x${window.screen.height}`
        });
        
        // 创建表单数据
        const formData = new FormData();
        formData.append('signature_data', signatureData);
        formData.append('metadata', metadata);
        
        // 发送签名数据到服务器
        fetch('{{ url_for("main.add_signature", file_id=file.id) }}', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('{{ t("Signature saved successfully") }}');
                location.reload(); // 重新加载页面显示新签名
            } else {
                // 检查是否是数据库结构错误
                if (data.error_type === 'db_structure') {
                    // 显示数据库结构警告
                    document.getElementById('db-structure-warning').style.display = 'block';
                }
                alert(data.message || '{{ t("Failed to save signature") }}');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('{{ t("An error occurred while saving the signature") }}');
        });
    });
    
    // 检查是否有flash消息提示数据库结构问题
    document.addEventListener('DOMContentLoaded', function() {
        // 如果页面有flash消息包含"数据库结构"，显示警告
        const flashMessages = document.querySelectorAll('.alert-warning');
        for (let i = 0; i < flashMessages.length; i++) {
            if (flashMessages[i].textContent.includes('数据库结构')) {
                document.getElementById('db-structure-warning').style.display = 'block';
                break;
            }
        }
    });
    
    // 验证签名
    document.querySelectorAll('.verify-signature').forEach(button => {
        button.addEventListener('click', function() {
            const signatureId = this.getAttribute('data-signature-id');
            fetch(`{{ url_for('main.verify_signature', signature_id=0) }}`.replace('0', signatureId))
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 显示验证信息
                    const verifyContent = document.getElementById('signatureVerifyContent');
                    const signature = data.signature;
                    
                    let metadataHtml = '';
                    if (signature.metadata) {
                        const metadata = signature.metadata;
                        metadataHtml = `
                        <div class="metadata mt-3">
                            <h6>{{ t('Additional Information') }}:</h6>
                            <ul class="list-group">
                                ${metadata.browser ? `<li class="list-group-item"><strong>{{ t('Browser') }}:</strong> ${metadata.browser}</li>` : ''}
                                ${metadata.timestamp ? `<li class="list-group-item"><strong>{{ t('Timestamp') }}:</strong> ${new Date(metadata.timestamp).toLocaleString()}</li>` : ''}
                                ${metadata.screen_size ? `<li class="list-group-item"><strong>{{ t('Screen Size') }}:</strong> ${metadata.screen_size}</li>` : ''}
                            </ul>
                        </div>`;
                    }
                    
                    verifyContent.innerHTML = `
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i> {{ t('Signature Verified') }}
                    </div>
                    <div class="signature-details">
                        <div class="row mb-2">
                            <div class="col-4"><strong>{{ t('Signer') }}:</strong></div>
                            <div class="col-8">${signature.user}</div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-4"><strong>{{ t('Date') }}:</strong></div>
                            <div class="col-8">${signature.date}</div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-4"><strong>{{ t('Document') }}:</strong></div>
                            <div class="col-8">${signature.file}</div>
                        </div>
                    </div>
                    ${metadataHtml}
                    `;
                    
                    // 显示模态框
                    const verifyModal = new bootstrap.Modal(document.getElementById('signatureVerifyModal'));
                    verifyModal.show();
                } else {
                    alert(data.message || '{{ t("Failed to verify signature") }}');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('{{ t("An error occurred while verifying the signature") }}');
            });
        });
    });
    
    // 删除签名 - 打开确认窗口
    document.querySelectorAll('.delete-signature').forEach(button => {
        button.addEventListener('click', function() {
            const signatureId = this.getAttribute('data-signature-id');
            document.getElementById('signatureIdToDelete').value = signatureId;
            
            const deleteModal = new bootstrap.Modal(document.getElementById('deleteSignatureModal'));
            deleteModal.show();
        });
    });
    
    // 确认删除签名
    document.getElementById('confirmDeleteSignature').addEventListener('click', function() {
        const signatureId = document.getElementById('signatureIdToDelete').value;
        
        fetch(`{{ url_for('main.delete_signature', signature_id=0) }}`.replace('0', signatureId), {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('{{ t("Signature deleted successfully") }}');
                location.reload(); // 重新加载页面更新显示
            } else {
                alert(data.message || '{{ t("Failed to delete signature") }}');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('{{ t("An error occurred while deleting the signature") }}');
        });
        
        // 关闭模态框
        const deleteModal = bootstrap.Modal.getInstance(document.getElementById('deleteSignatureModal'));
        deleteModal.hide();
    });
});
</script>
{% endblock %} 