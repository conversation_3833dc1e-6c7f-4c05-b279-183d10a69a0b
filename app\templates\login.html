{% extends "base.html" %}
{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <h2>Login</h2>
        {% with messages = get_flashed_messages() %}
        {% if messages %}
        <div class="alert alert-danger">
            {{ messages[0] }}
        </div>
        {% endif %}
        {% endwith %}
        <form method="POST" action="{{ url_for('auth.login') }}">
            <div class="mb-3">
                <label for="username" class="form-label">Username</label>
                <input type="text" class="form-control" id="username" name="username" required>
            </div>
            <div class="mb-3">
                <label for="password" class="form-label">Password</label>
                <input type="password" class="form-control" id="password" name="password" required>
            </div>
            <div class="mb-3">
                <label for="captcha" class="form-label">验证码</label>
                <div class="input-group">
                    <input type="text" class="form-control" id="captcha" name="captcha" required autocomplete="off" maxlength="4">
                    <div class="input-group-append">
                        <span class="input-group-text p-1" style="background-color: transparent; border: none;">
                            <img id="captcha-image" src="{{ url_for('auth.captcha') }}" alt="验证码" style="height: 50px; width: 150px; cursor: pointer;" onclick="refreshCaptcha()">
                        </span>
                    </div>
                </div>
                <small class="form-text text-muted">点击图片刷新验证码，不区分大小写</small>
            </div>
            <button type="submit" class="btn btn-primary">Login</button>
        </form>
    </div>
</div>

<script>
function refreshCaptcha() {
    var img = document.getElementById('captcha-image');
    img.src = "{{ url_for('auth.captcha') }}?" + new Date().getTime();
    document.getElementById('captcha').value = '';
    document.getElementById('captcha').focus();
}

// 页面加载完成后自动刷新一次验证码
document.addEventListener('DOMContentLoaded', function() {
    refreshCaptcha();
});
</script>
{% endblock %} 