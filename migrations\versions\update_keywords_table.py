"""update keywords table

Revision ID: update_keywords_table
Revises: 
Create Date: 2024-03-21 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'update_keywords_table'
down_revision = None  # 表示这是第一个迁移
branch_labels = None
depends_on = None

def upgrade():
    # 创建新的关键字表
    op.create_table('keywords',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.<PERSON>umn('word', sa.String(length=100), nullable=False),
        sa.Column('folder_id', sa.Integer(), nullable=True),
        sa.Column('keyword_type', sa.Integer(), nullable=False, server_default='0'),
        sa.<PERSON>umn('create_date', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['folder_id'], ['folder.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # 创建索引
    op.create_index('ix_keywords_word', 'keywords', ['word'])
    op.create_index('ix_keywords_folder_id', 'keywords', ['folder_id'])
    op.create_index('ix_keywords_keyword_type', 'keywords', ['keyword_type'])
    
    # 迁移现有数据（如果有的话）
    # op.execute('INSERT INTO keywords (word, folder_id, keyword_type, create_date) SELECT word, folder_id, 0, create_date FROM old_keywords')

def downgrade():
    # 删除索引
    op.drop_index('ix_keywords_word')
    op.drop_index('ix_keywords_folder_id')
    op.drop_index('ix_keywords_keyword_type')
    
    # 删除表
    op.drop_table('keywords') 