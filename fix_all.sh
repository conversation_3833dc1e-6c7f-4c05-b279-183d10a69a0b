#!/bin/bash

echo "===== PDF手写签名功能一键修复工具 ====="
echo ""

# 确保脚本在项目根目录下运行
if [ ! -f "run.py" ]; then
    echo "错误: 未找到run.py文件"
    echo "请在项目根目录下运行此脚本"
    exit 1
fi

# 第一步: 安装必要依赖
echo "步骤1: 安装必要依赖..."
echo "----------------------------"

# 确保pip已安装
if ! command -v pip3 &> /dev/null; then
    echo "正在安装pip..."
    sudo apt-get update
    sudo apt-get install -y python3-pip
fi

# 安装必要的Python库
echo "安装img2pdf和pillow库..."
pip3 install img2pdf pillow

# 检查安装结果
if ! python3 -c "import img2pdf; import PIL" 2>/dev/null; then
    echo "警告: 依赖可能未正确安装，请检查上方错误信息"
    echo "您可以尝试手动安装:"
    echo "  pip3 install img2pdf pillow"
    echo ""
    echo "是否继续修复过程? (y/n)"
    read -r continue_fix
    if [ "$continue_fix" != "y" ] && [ "$continue_fix" != "Y" ]; then
        exit 1
    fi
else
    echo "依赖安装成功！"
fi

echo ""
echo "步骤2: 修复PDF签名模板..."
echo "----------------------------"

# 配置
TEMPLATE_DIR="app/templates/preview"
PDF_SIGN_TEMPLATE="$TEMPLATE_DIR/pdf_sign.html"
BACKUP_DIR="backups"

# 确保备份目录存在
if [ ! -d "$BACKUP_DIR" ]; then
    mkdir -p "$BACKUP_DIR"
    echo "创建备份目录: $BACKUP_DIR"
fi

# 检查模板文件是否存在
if [ ! -f "$PDF_SIGN_TEMPLATE" ]; then
    echo "错误: 未找到PDF签名模板 $PDF_SIGN_TEMPLATE"
    exit 1
fi

# 备份原始文件
TIMESTAMP=$(date +"%Y%m%d%H%M%S")
BACKUP_FILE="$BACKUP_DIR/$(basename "$PDF_SIGN_TEMPLATE").$TIMESTAMP.bak"
cp "$PDF_SIGN_TEMPLATE" "$BACKUP_FILE"
echo "已备份原始文件: $BACKUP_FILE"

# 修复模板文件
echo "修复PDF签名模板..."

# 使用sed命令进行修改
# 1. 添加z-index到iframe
sed -i 's/<iframe id="pdfViewer" src="{{ file_url }}" style="\([^"]*\)"/<iframe id="pdfViewer" src="{{ file_url }}" style="\1 z-index: 1;"/g' "$PDF_SIGN_TEMPLATE"

# 2. 添加z-index到签名层
sed -i 's/<div id="signatureLayer" class="position-absolute top-0 start-0 w-100 h-100" style="\([^"]*\)"/<div id="signatureLayer" class="position-absolute top-0 start-0 w-100 h-100" style="\1 z-index: 2;"/g' "$PDF_SIGN_TEMPLATE"

# 3. 添加或替换CSS样式
CSS_FIXES='
    .signature-item {
        position: absolute;
        cursor: move;
        z-index: 100;
        pointer-events: auto !important;
    }
    
    .signature-image {
        max-width: 200px;
        border: 1px dashed transparent;
    }
    
    .signature-item:hover .signature-image {
        border-color: #007bff;
    }
    
    .signature-controls {
        display: none;
        z-index: 101;
    }
    
    .signature-item:hover .signature-controls {
        display: block;
    }
    
    /* 确保签名层位于iframe之上 */
    #signatureLayer {
        z-index: 50;
        pointer-events: auto !important;
    }
    
    /* 确保iframe不会拦截鼠标事件 */
    #pdfViewer {
        pointer-events: none;
    }
    
    /* 确保签名模态框显示在最上层 */
    .modal {
        z-index: 9999;
    }
    
    /* 修复移动设备上的触摸事件 */
    @media (hover: none) and (pointer: coarse) {
        #signatureLayer {
            touch-action: none;
        }
        
        .signature-item {
            touch-action: none;
        }
    }'

# 检查是否有style标签，如果有则替换内容，如果没有则添加
if grep -q "<style>" "$PDF_SIGN_TEMPLATE"; then
    # 使用awk替换style标签内容
    awk -v css="$CSS_FIXES" '
    /<style>/ {
        print "<style>";
        print css;
        in_style = 1;
        next;
    }
    /<\/style>/ {
        print "</style>";
        in_style = 0;
        next;
    }
    !in_style {
        print;
    }
    ' "$PDF_SIGN_TEMPLATE" > "$PDF_SIGN_TEMPLATE.tmp" && mv "$PDF_SIGN_TEMPLATE.tmp" "$PDF_SIGN_TEMPLATE"
else
    # 在head结束标签前添加style标签
    sed -i "s/<\/head>/<style>$CSS_FIXES<\/style>\n<\/head>/g" "$PDF_SIGN_TEMPLATE"
fi

# 4. 修改JavaScript事件监听器
sed -i 's/pdfContainer\.addEventListener('"'"'click'"'"', function(e)/signatureLayer.addEventListener('"'"'click'"'"', function(e)/g' "$PDF_SIGN_TEMPLATE"

# 5. 添加阻止事件冒泡的代码
sed -i '/deleteBtn\.addEventListener('"'"'click'"'"', function()/s/function()/function(e)\n            e.preventDefault();\n            e.stopPropagation();/g' "$PDF_SIGN_TEMPLATE"

# 6. 修改iframe加载事件
IFRAME_LOAD_CODE='
        pdfViewer.onload = function() {
            console.log("PDF iframe已加载完成");
            // 确保签名层可交互
            signatureLayer.style.pointerEvents = '"'"'auto'"'"';
            // 移除iframe的pointer-events，这样点击事件可以传递到签名层
            pdfViewer.style.pointerEvents = '"'"'none'"'"';
            
            console.log("调试信息 - 签名层样式:", {
                pointerEvents: signatureLayer.style.pointerEvents,
                zIndex: signatureLayer.style.zIndex,
                width: signatureLayer.offsetWidth,
                height: signatureLayer.offsetHeight
            });
        };'

# 使用awk替换iframe加载事件
awk -v load_code="$IFRAME_LOAD_CODE" '
/pdfViewer\.onload = function\(\)/ {
    print load_code;
    in_onload = 1;
    next;
}
/};/ {
    if (in_onload) {
        in_onload = 0;
        next;
    }
    print;
    next;
}
!in_onload {
    print;
}
' "$PDF_SIGN_TEMPLATE" > "$PDF_SIGN_TEMPLATE.tmp" && mv "$PDF_SIGN_TEMPLATE.tmp" "$PDF_SIGN_TEMPLATE"

# 7. 添加触摸设备支持代码
TOUCH_SUPPORT_CODE='
    // 添加触摸设备支持
    function addTouchEvents() {
        signatureLayer.addEventListener('"'"'touchstart'"'"', function(e) {
            console.log("触摸开始事件触发");
            // 如果用户正在拖动签名，则不触发点击事件
            if (isDragging) return;
            
            // 获取第一个触摸点
            const touch = e.touches[0];
            
            // 忽略已有签名上的触摸
            if (touch.target.closest('\''.signature-item\'')) {
                console.log("触摸了现有签名，忽略");
                return;
            }
            
            // 计算触摸位置
            const rect = pdfContainer.getBoundingClientRect();
            const x = touch.clientX - rect.left;
            const y = touch.clientY - rect.top;
            
            console.log(`触摸位置: x=${x}, y=${y}`);
            
            // 暂存位置信息
            currentSignature = { x, y };
            
            // 显示签名板
            signatureModal.show();
            
            // 阻止默认行为和冒泡
            e.preventDefault();
        });
        
        // 给签名项添加触摸拖拽支持
        signatureLayer.addEventListener('"'"'touchmove'"'"', function(e) {
            if (!isDragging || !currentSignature) return;
            
            // 阻止页面滚动
            e.preventDefault();
            
            const touch = e.touches[0];
            const rect = pdfContainer.getBoundingClientRect();
            const x = touch.clientX - rect.left - offsetX;
            const y = touch.clientY - rect.top - offsetY;
            
            // 限制在PDF容器内
            const maxX = rect.width - currentSignature.offsetWidth;
            const maxY = rect.height - currentSignature.offsetHeight;
            const boundedX = Math.max(0, Math.min(x, maxX));
            const boundedY = Math.max(0, Math.min(y, maxY));
            
            currentSignature.style.left = boundedX + '"'"'px'"'"';
            currentSignature.style.top = boundedY + '"'"'px'"'"';
            
            // 更新数组中的位置
            const index = signatureItems.findIndex(item => item.element === currentSignature);
            if (index !== -1) {
                signatureItems[index].x = boundedX;
                signatureItems[index].y = boundedY;
            }
        });
        
        signatureLayer.addEventListener('"'"'touchend'"'"', function() {
            if (isDragging) {
                console.log("触摸拖拽结束");
                if (currentSignature) {
                    currentSignature.style.zIndex = '"'"'100'"'"';
                }
            }
            isDragging = false;
            currentSignature = null;
        });
    }
    
    // 添加触摸事件支持
    if ('"'"'ontouchstart'"'"' in window) {
        console.log("检测到触摸设备，添加触摸事件支持");
        addTouchEvents();
    }'

# 在初始化完成前添加触摸支持代码
sed -i 's/console.log("PDF签名页面初始化完成");/'"$TOUCH_SUPPORT_CODE"'\n    console.log("PDF签名页面初始化完成");/g' "$PDF_SIGN_TEMPLATE"

echo "PDF签名模板修复完成!"

echo ""
echo "步骤3: 下载必要的JavaScript库..."
echo "----------------------------"

# 创建必要的目录
mkdir -p static/js/libs

# 下载html2canvas库
echo "下载html2canvas库..."
curl -s -o static/js/libs/html2canvas.min.js https://unpkg.com/html2canvas@1.4.1/dist/html2canvas.min.js
if [ $? -eq 0 ]; then
    echo "html2canvas库下载成功"
else
    echo "警告: html2canvas库下载失败"
fi

# 下载signature_pad库
echo "下载signature_pad库..."
curl -s -o static/js/libs/signature_pad.umd.min.js https://cdn.jsdelivr.net/npm/signature_pad@4.0.0/dist/signature_pad.umd.min.js
if [ $? -eq 0 ]; then
    echo "signature_pad库下载成功"
else
    echo "警告: signature_pad库下载失败"
fi

echo ""
echo "===== 修复完成 ====="
echo ""
echo "请重启Web应用以应用更改:"
echo "  python3 run.py" 