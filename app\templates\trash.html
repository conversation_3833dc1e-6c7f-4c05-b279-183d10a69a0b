{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>Trash</h2>
        {% if current_user.is_admin %}
        <button class="btn btn-danger" onclick="emptyTrash()">
            <i class="fas fa-trash-alt"></i> Empty Trash
        </button>
        {% endif %}
    </div>
    
    <!-- 文件夹列表 -->
    {% if folders %}
    <h4>Deleted Folders</h4>
    <div class="table-responsive mb-4">
        <table class="table">
            <thead>
                <tr>
                    <th>Folder Name</th>
                    <th>Delete Time</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for folder in folders %}
                <tr>
                    <td>{{ folder.name }}</td>
                    <td>{{ folder.delete_time|format_datetime }}</td>
                    <td>
                        <button class="btn btn-sm btn-primary" onclick="restoreFolder({{ folder.id }})">Restore</button>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% endif %}
    
    <!-- 文件列表 -->
    {% if files %}
    <h4>Deleted Files</h4>
    <div class="table-responsive">
        <table class="table">
            <thead>
                <tr>
                    <th>Filename</th>
                    <th>Type</th>
                    <th>Size</th>
                    <th>Delete Time</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for file in files %}
                <tr>
                    <td>{{ file.original_filename }}</td>
                    <td>{{ file.file_type }}</td>
                    <td>{{ file.file_size|format_size }}</td>
                    <td>{{ file.delete_time|format_datetime }}</td>
                    <td>
                        <button class="btn btn-sm btn-primary" onclick="restoreFile({{ file.id }})">Restore</button>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% endif %}
    
    {% if not files and not folders %}
    <div class="alert alert-info">Trash is empty</div>
    {% endif %}
</div>

<script>
// 恢复文件
function restoreFile(fileId) {
    if (confirm('Are you sure you want to restore this file?')) {
        fetch(`/restore_file/${fileId}`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.reload();
            } else {
                alert(data.message || 'Restore failed');
            }
        })
        .catch(error => alert('Operation failed'));
    }
}

// 恢复文件夹
function restoreFolder(folderId) {
    if (confirm('Are you sure you want to restore this folder?')) {
        fetch(`/restore_folder/${folderId}`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.reload();
            } else {
                alert(data.message || 'Restore failed');
            }
        })
        .catch(error => alert('Operation failed'));
    }
}

// 清空回收站
function emptyTrash() {
    if (confirm('Are you sure you want to empty the trash? This action cannot be undone!')) {
        fetch('/empty_trash', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.reload();
            } else {
                alert(data.message || 'Empty failed');
            }
        })
        .catch(error => alert('Operation failed'));
    }
}
</script>
{% endblock %} 