#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
添加用户默认电子签名表迁移脚本
"""

import os
import sys
from datetime import datetime
import pytz

# 确保能够导入app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app, db

app = create_app()

def add_user_signature_table():
    """添加UserSignature表"""
    with app.app_context():
        try:
            # 检查表是否已存在
            print("检查UserSignature表是否存在...")
            inspector = db.inspect(db.engine)
            tables = inspector.get_table_names()
            
            if 'user_signature' in tables:
                print("UserSignature表已存在，无需创建")
                return
            
            # 创建表
            print("创建UserSignature表...")
            db.engine.execute("""
            CREATE TABLE user_signature (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                signature_data TEXT NOT NULL,
                created_at DATETIME,
                is_default BOOLEAN DEFAULT 1,
                description TEXT,
                FOREIGN KEY (user_id) REFERENCES user (id)
            )
            """)
            
            # 提交更改
            db.session.commit()
            print("UserSignature表创建成功!")
            
        except Exception as e:
            print(f"创建UserSignature表时出错: {str(e)}")
            db.session.rollback()
            raise

if __name__ == "__main__":
    add_user_signature_table() 