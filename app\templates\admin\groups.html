{% extends "base.html" %}

{% block content %}
<div class="container">
    <h2 class="mb-4">{{ t('User Group Management') }}</h2>
    
    <!-- 添加用户组按钮 -->
    <button class="btn btn-primary mb-4" onclick="showAddGroupModal()">
        <i class="fas fa-plus"></i> {{ t('Add Group') }}
    </button>
    
    <!-- 用户组列表 -->
    <div class="table-responsive">
        <table class="table table-hover">
            <thead class="table-light">
                <tr>
                    <th>{{ t('Group Name') }}</th>
                    <th>{{ t('Description') }}</th>
                    <th>{{ t('Member Count') }}</th>
                    <th>{{ t('Create Date') }}</th>
                    <th>{{ t('Actions') }}</th>
                </tr>
            </thead>
            <tbody>
                {% for group in groups %}
                <tr>
                    <td>{{ group.name }}</td>
                    <td>{{ group.description or '' }}</td>
                    <td>{{ group.users|length }}</td>
                    <td>{{ format_datetime(group.create_date) }}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="showEditGroupModal({{ group.id }}, '{{ group.name }}', '{{ group.description or '' }}')">
                                <i class="fas fa-edit"></i> {{ t('Edit') }}
                            </button>
                            <a href="{{ url_for('main.group_members', group_id=group.id) }}" class="btn btn-outline-info">
                                <i class="fas fa-users"></i> {{ t('Members') }}
                            </a>
                            <button class="btn btn-outline-danger" onclick="deleteGroup({{ group.id }})">
                                <i class="fas fa-trash"></i> {{ t('Delete') }}
                            </button>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<!-- 添加/编辑用户组模态框 -->
<div class="modal fade" id="groupModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="groupModalTitle">{{ t('Add Group') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="groupForm">
                    <input type="hidden" id="groupId">
                    <div class="mb-3">
                        <label class="form-label">{{ t('Group Name') }}</label>
                        <input type="text" class="form-control" id="groupName" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">{{ t('Description') }}</label>
                        <textarea class="form-control" id="groupDescription" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ t('Cancel') }}</button>
                <button type="button" class="btn btn-primary" onclick="saveGroup()">{{ t('Save') }}</button>
            </div>
        </div>
    </div>
</div>

<script>
let groupModal;

document.addEventListener('DOMContentLoaded', function() {
    groupModal = new bootstrap.Modal(document.getElementById('groupModal'));
});

function showAddGroupModal() {
    document.getElementById('groupModalTitle').textContent = '{{ t("Add Group") }}';
    document.getElementById('groupId').value = '';
    document.getElementById('groupName').value = '';
    document.getElementById('groupDescription').value = '';
    groupModal.show();
}

function showEditGroupModal(id, name, description) {
    document.getElementById('groupModalTitle').textContent = '{{ t("Edit Group") }}';
    document.getElementById('groupId').value = id;
    document.getElementById('groupName').value = name;
    document.getElementById('groupDescription').value = description;
    groupModal.show();
}

function saveGroup() {
    const id = document.getElementById('groupId').value;
    const name = document.getElementById('groupName').value;
    const description = document.getElementById('groupDescription').value;
    
    if (!name) {
        alert('{{ t("Group name cannot be empty") }}');
        return;
    }
    
    const url = id ? `/admin/groups/${id}/edit` : '/admin/groups/add';
    
    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `name=${encodeURIComponent(name)}&description=${encodeURIComponent(description)}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('{{ t("An error occurred while saving the group") }}');
    });
}

function deleteGroup(id) {
    if (!confirm('{{ t("Are you sure you want to delete this group?") }}')) {
        return;
    }
    
    fetch(`/admin/groups/${id}/delete`, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('{{ t("An error occurred while deleting the group") }}');
    });
}
</script>
{% endblock %} 