"""Add user permissions

Revision ID: 47bfdfb21fbb
Revises: 
Create Date: 2025-01-21 14:17:20.428348

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '47bfdfb21fbb'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('user', schema=None) as batch_op:
        batch_op.add_column(sa.Column('can_upload', sa.<PERSON>(), nullable=True))
        batch_op.add_column(sa.Column('can_download', sa.<PERSON>(), nullable=True))
        batch_op.add_column(sa.Column('can_delete', sa.<PERSON>(), nullable=True))
        batch_op.add_column(sa.Column('can_share', sa.<PERSON>an(), nullable=True))
        batch_op.add_column(sa.Column('storage_limit', sa.Integer(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('user', schema=None) as batch_op:
        batch_op.drop_column('storage_limit')
        batch_op.drop_column('can_share')
        batch_op.drop_column('can_delete')
        batch_op.drop_column('can_download')
        batch_op.drop_column('can_upload')

    # ### end Alembic commands ###
