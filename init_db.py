from app import create_app, db
from app.models import User, File, Folder

def init_db():
    app = create_app()
    with app.app_context():
        # 删除所有表并重新创建
        db.drop_all()
        db.create_all()
        
        # 创建管理员账户
        admin = User(username='cv24051', is_admin=True)
        admin.set_password('admin123')
        db.session.add(admin)
        
        # 创建普通用户示例
        user = User(username='林志勇', is_admin=True)
        user.set_password('lzy2025')
        db.session.add(user)
        
        # 提交更改
        db.session.commit()
        print("数据库初始化完成")
        print("\n管理员账户:")
        print("用户名: cv24051")
        print("密码: admin123")
        print("\n普通用户:")
        print("用户名: 林志勇")
        print("密码: lzy2025")

if __name__ == '__main__':
    init_db() 