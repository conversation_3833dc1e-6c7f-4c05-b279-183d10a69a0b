from app import create_app, db
from app.models import User, File, Signature
import os

def apply_migration():
    """应用数据库迁移，添加电子签名模型"""
    app = create_app()
    
    with app.app_context():
        # 检查表是否存在
        inspector = db.inspect(db.engine)
        existing_tables = inspector.get_table_names()
        
        if 'signature' not in existing_tables:
            print("创建电子签名表...")
            
            # 创建签名表
            db.engine.execute("""
            CREATE TABLE signature (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                file_id INTEGER NOT NULL,
                signature_data TEXT NOT NULL,
                signature_date DATETIME,
                ip_address VARCHAR(50),
                signature_metadata TEXT,
                FOREIGN KEY (user_id) REFERENCES user (id),
                FOREIGN KEY (file_id) REFERENCES file (id)
            )
            """)
            
            print("电子签名表创建成功")
        else:
            print("电子签名表已存在")

if __name__ == '__main__':
    apply_migration() 