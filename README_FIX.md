# PDF手写签名功能修复指南

本文档提供了修复PDF手写签名功能的详细步骤和说明。

## 问题描述

当前PDF手写签名功能存在以下问题：

1. **缺少Python依赖**：
   - 报错: `ModuleNotFoundError: No module named 'img2pdf'`

2. **数据库模型缺少必要字段**：
   - 报错: `type object 'UserSignature' has no attribute 'signature_id'`
   - 报错: `'File' object has no attribute 'physical_path'`

3. **前端交互问题**：
   - 无法在PDF上点击添加签名
   - 放置的签名无法移动或删除
   - 移动设备上可能无法正常使用

## 修复方法

### 方法一：使用一键修复脚本（推荐）

我们提供了一个综合修复脚本，可以一次解决所有问题。

#### 在Ubuntu服务器上运行

1. 将以下文件上传到项目根目录（与`run.py`在同一目录）：
   - `fix_all_models.sh`
   - `fix_db_models.py`（SQLite数据库）
   - `fix_db_mysql.py`（MySQL数据库）

2. 赋予脚本执行权限：
   ```bash
   chmod +x fix_all_models.sh
   ```

3. 运行一键修复脚本：
   ```bash
   ./fix_all_models.sh
   ```

4. 按照提示操作，脚本会自动：
   - 安装缺少的Python依赖
   - 修复数据库模型
   - 修复前端交互问题
   - 下载必要的JavaScript库

5. 修复完成后，重启应用程序：
   ```bash
   python3 run.py
   ```

### 方法二：分步修复

如果您希望逐步解决问题，可以按以下步骤操作：

#### 1. 安装缺少的Python依赖

```bash
pip3 install img2pdf pillow
```

#### 2. 修复数据库模型

根据您使用的数据库类型，运行对应的脚本：

- SQLite数据库：
  ```bash
  python3 fix_db_models.py
  ```

- MySQL数据库：
  ```bash
  pip3 install pymysql  # 先安装pymysql库
  python3 fix_db_mysql.py
  ```

#### 3. 修复前端交互问题

修改`app/templates/preview/pdf_sign.html`文件：

- 添加z-index到iframe和签名层
- 修改pointer-events属性
- 将事件监听器从pdfContainer改为signatureLayer
- 添加触摸设备支持

这些修改已经包含在`fix_all_models.sh`脚本中，建议使用脚本自动修复。

## 验证修复

修复完成后，请验证以下功能：

1. 应用程序能否正常启动，不再出现`ModuleNotFoundError`
2. 能否访问签名页面，不再出现数据库字段缺失错误
3. 能否点击PDF任意位置打开签名模态框
4. 能否添加签名到PDF上
5. 能否移动已添加的签名
6. 能否删除已添加的签名
7. 在移动设备上测试以上功能

## 常见问题

### 脚本执行权限问题

如果无法执行脚本，请确保已赋予执行权限：

```bash
chmod +x fix_all_models.sh
```

### 安装Python依赖时出错

如果`pip3`安装依赖时出错，可以尝试以下方法：

1. 更新pip：
   ```bash
   pip3 install --upgrade pip
   ```

2. 单独安装每个依赖：
   ```bash
   pip3 install img2pdf
   pip3 install pillow
   pip3 install pymysql
   ```

### 数据库修复失败

如果数据库修复失败，请检查：

1. 数据库配置是否正确
2. 数据库用户是否有修改表结构的权限
3. 查看具体的错误信息，根据错误提示解决

### 修复后仍然无法点击添加签名

如果修复后仍然无法点击PDF添加签名，请尝试：

1. 清除浏览器缓存
2. 检查浏览器控制台是否有JavaScript错误
3. 确保服务器已重启，应用了最新的修改

## 脚本文件说明

- `fix_all_models.sh`：一键修复脚本，集成所有修复功能
- `fix_db_models.py`：SQLite数据库模型修复脚本
- `fix_db_mysql.py`：MySQL数据库模型修复脚本
- `install_dependencies.sh`：仅安装Python依赖的脚本
- `fix_signature_ui.py`：仅修复前端交互问题的Python脚本

## 联系支持

如有任何问题，请联系技术支持团队。 