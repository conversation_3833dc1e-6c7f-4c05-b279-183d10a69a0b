/**
 * 处理语言切换功能
 */
function switchLanguage(lang) {
    // 发送语言切换请求
    fetch('/set_language/' + lang, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            // 成功切换语言后刷新页面
            window.location.reload();
        } else {
            // 显示错误消息
            alert(data.message || 'Failed to change language');
        }
    })
    .catch(error => {
        console.error('Error switching language:', error);
        alert('Failed to change language. Please try again.');
    });
} 